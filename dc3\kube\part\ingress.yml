#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

---
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: dc3-ingress-class
  namespace: dc3
  labels:
    dc3.version: 2022.1.0
spec:
  controller: k8s.io/ingress-nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dc3-ingress
  namespace: dc3
  labels:
    dc3.version: 2022.1.0
spec:
  ingressClassName: dc3-ingress-class
  rules:
    - http:
        paths:
          - path: /dc3/register
            pathType: Prefix
            backend:
              service:
                name: dc3-center-register
                port:
                  number: 8100
          - path: /dc3/monitor
            pathType: Prefix
            backend:
              service:
                name: dc3-center-monitor
                port:
                  number: 8200
          - path: /eureka/
            pathType: Prefix
            backend:
              service:
                name: dc3-center-register
                port:
                  number: 8100
          - path: /monitor/
            pathType: Prefix
            backend:
              service:
                name: dc3-center-monitor
                port:
                  number: 8200