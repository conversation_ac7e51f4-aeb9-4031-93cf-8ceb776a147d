#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
data:
  point:
    batch:
      speed: ${POINT_BATCH_SPEED:100}
      interval: ${POINT_BATCH_INTERVAL:5}
    sava:
      influxdb:
        enable: false
      opentsdb:
        enable: false
        host: dc3-opentsdb
        port: 4242
      elasticsearch:
        enable: false
        host: dc3-elasticsearch
        port: 9200

server:
  port: 8500

spring:
  application:
    name: @project.artifactId@
  profiles:
    active:
      - server
      - register
      - monitor
      - rabbitmq
      - mysql
      - mongo
      - redis
      - feign
      - ${NODE_ENV:dev}
  main:
    allow-bean-definition-overriding: true
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  redis:
    database: 3
  cache:
    redis:
      time-to-live: ${CACHE_REDIS_TIME_TO_LIVE:12H}

logging:
  level:
    io.github.pnoker: DEBUG
    io.github.pnoker.center.data.service.job: INFO
    io.github.pnoker.center.data.service.rabbit: INFO
  file:
    name: dc3/logs/center/data/${spring.application.name}.log