{"properties": [{"name": "data.point.batch.speed", "type": "java.lang.Integer", "description": "critical value of point value batch saving speed."}, {"name": "data.point.batch.interval", "type": "java.lang.Integer", "description": "point value batch saving cycle."}, {"name": "data.point.sava.influxdb.enable", "type": "java.lang.Bo<PERSON>an", "description": "point value can save to influxdb."}, {"name": "data.point.sava.influxdb.host", "type": "java.lang.String", "description": "point value save to influxdb host."}, {"name": "data.point.sava.influxdb.port", "type": "java.lang.Integer", "description": "point value save to influxdb port."}, {"name": "data.point.sava.opentsdb.enable", "type": "java.lang.Bo<PERSON>an", "description": "point value can save to opentsdb."}, {"name": "data.point.sava.opentsdb.host", "type": "java.lang.String", "description": "point value save to opentsdb host."}, {"name": "data.point.sava.opentsdb.port", "type": "java.lang.Integer", "description": "point value save to opentsdb port."}, {"name": "data.point.sava.elasticsearch.enable", "type": "java.lang.Bo<PERSON>an", "description": "point value can save to elasticsearch."}, {"name": "data.point.sava.elasticsearch.host", "type": "java.lang.String", "description": "point value save to elasticsearch host."}, {"name": "data.point.sava.elasticsearch.port", "type": "java.lang.Integer", "description": "point value save to elasticsearch port."}, {"name": "rtmp.ffmpeg.window", "type": "java.lang.String", "description": "ffmpeg window path."}, {"name": "rtmp.ffmpeg.unix", "type": "java.lang.String", "description": "ffmpeg unix path."}, {"name": "server.thread.prefix", "type": "java.lang.String", "description": "thread prefix."}, {"name": "server.thread.core-pool-size", "type": "java.lang.Integer", "description": "thread core pool size."}, {"name": "server.thread.maximum-pool-size", "type": "java.lang.Integer", "description": "thread maximum pool size."}, {"name": "server.thread.keep-alive-time", "type": "java.lang.Integer", "description": "thread keep alive time."}, {"name": "driver.name", "type": "java.lang.String", "description": "driver name."}, {"name": "driver.type", "type": "java.lang.String", "description": "driver type, default:driver, driver|gateway...,."}, {"name": "driver.tenant", "type": "java.lang.String", "description": "driver tenant."}, {"name": "driver.project", "type": "java.lang.String", "description": "driver project."}, {"name": "driver.description", "type": "java.lang.String", "description": "driver description."}, {"name": "driver.driver-attribute", "type": "java.util.List", "description": "driver attribute list<p/><p>- displayName: attribute display name<p>- name: attribute name<p>- type: string/int/double/float/long/boolean<p>- value: default value<p>- description: description."}, {"name": "driver.point-attribute", "type": "java.util.List", "description": "device point attribute list<p/><p>- displayName: attribute display name<p>- name: attribute name<p>- type: string/int/double/float/long/boolean<p>- value: default value<p>- description: description."}, {"name": "driver.schedule.status.enable", "type": "java.lang.Bo<PERSON>an", "description": "driver status schedule status."}, {"name": "driver.schedule.status.corn", "type": "java.lang.String", "description": "driver status schedule corn."}, {"name": "driver.schedule.read.enable", "type": "java.lang.Bo<PERSON>an", "description": "driver read schedule status."}, {"name": "driver.schedule.read.corn", "type": "java.lang.String", "description": "driver read schedule corn."}, {"name": "driver.schedule.custom.enable", "type": "java.lang.Bo<PERSON>an", "description": "driver custom schedule status."}, {"name": "driver.schedule.custom.corn", "type": "java.lang.String", "description": "driver custom schedule corn."}, {"name": "driver.custom", "type": "java.lang.String", "description": "driver custom config."}, {"name": "driver.mqtt.username", "type": "java.lang.String", "description": "driver username for mqtt server."}, {"name": "driver.mqtt.password", "type": "java.lang.String", "description": "driver password for mqtt server."}, {"name": "driver.mqtt.url", "type": "java.lang.String", "description": "driver url for mqtt server."}, {"name": "driver.mqtt.keep-alive", "type": "java.lang.Integer", "description": "driver keep alive interval for mqtt server."}, {"name": "driver.mqtt.completion-timeout", "type": "java.lang.Integer", "description": "driver completion timeout for mqtt server."}, {"name": "driver.mqtt.auth-type", "type": "io.github.pnoker.common.sdk.bean.mqtt.MqttProperties.AuthTypeEnum", "description": "mqtt auth type."}, {"name": "driver.mqtt.receive-topics", "type": "java.util.List<io.github.pnoker.common.sdk.bean.mqtt.MqttProperties.Topic>", "description": "driver receive topics(string list) for mqtt server."}, {"name": "driver.mqtt.default-send-topic", "type": "io.github.pnoker.common.sdk.bean.mqtt.MqttProperties.Topic", "description": "driver default sent topic for mqtt server."}, {"name": "driver.mqtt.default-send-topic.name", "type": "java.lang.String", "description": "driver default sent topic name for mqtt server."}, {"name": "driver.mqtt.default-send-topic.qos", "type": "java.lang.Integer", "description": "driver default sent topic qos for mqtt server."}, {"name": "driver.mqtt.client", "type": "java.lang.String", "description": "driver client id for mqtt server."}, {"name": "driver.mqtt.ca-crt", "type": "java.lang.String", "description": "driver ca crt for mqtt server."}, {"name": "driver.mqtt.client-key-pass", "type": "java.lang.String", "description": "driver client key pass for mqtt server."}, {"name": "driver.mqtt.client-key", "type": "java.lang.String", "description": "driver client key for mqtt server."}, {"name": "driver.mqtt.client-crt", "type": "java.lang.String", "description": "driver client crt for mqtt server."}, {"name": "driver.mqtt.batch.speed", "type": "java.lang.Integer", "description": "critical value of mqtt message batch saving speed."}, {"name": "driver.mqtt.batch.interval", "type": "java.lang.Integer", "description": "mqtt message batch saving cycle."}]}