#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## JWT Auth Plugin
##--------------------------------------------------------------------

## HMAC Hash Secret.
##
## Value: String
auth.jwt.secret = emqxsecret

## From where the JWT string can be got
##
## Value: username | password
## Default: password
auth.jwt.from = password

## RSA or ECDSA public key file.
##
## Value: File
## auth.jwt.pubkey = etc/certs/jwt_public_key.pem

## Enable to verify claims fields
##
## Value: on | off
auth.jwt.verify_claims = off

## The checklist of claims to validate
##
## Value: String
## auth.jwt.verify_claims.$name = expected
##
## Variables:
##  - %u: username
##  - %c: clientid
# auth.jwt.verify_claims.username = %u

## The Signature format
##   - `der`: The erlang default format
##   - `raw`: Compatible with others platform maybe
#auth.jwt.signature_format = der
