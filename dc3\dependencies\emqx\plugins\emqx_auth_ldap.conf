#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## LDAP Auth Plugin
##--------------------------------------------------------------------

## LDAP server list, seperated by ','.
##
## Value: String
auth.ldap.servers = 127.0.0.1

## LDAP server port.
##
## Value: Port
auth.ldap.port = 389

## LDAP pool size
##
## Value: String
auth.ldap.pool = 8

## LDAP Bind DN.
##
## Value: DN
auth.ldap.bind_dn = cn=root,dc=emqx,dc=io

## LDAP Bind Password.
##
## Value: String
auth.ldap.bind_password = public

## LDAP query timeout.
##
## Value: Number
auth.ldap.timeout = 30s

## Device DN.
##
## Variables:
##
## Value: DN
auth.ldap.device_dn = ou=device,dc=emqx,dc=io

## Specified ObjectClass
##
## Variables:
##
## Value: string
auth.ldap.match_objectclass = mqttUser

## attributetype for username
##
## Variables:
##
## Value: string
auth.ldap.username.attributetype = uid

## attributetype for password
##
## Variables:
##
## Value: string
auth.ldap.password.attributetype = userPassword

## Whether to enable SSL.
##
## Value: true | false
auth.ldap.ssl = false

#auth.ldap.ssl.certfile = etc/certs/cert.pem

#auth.ldap.ssl.keyfile = etc/certs/key.pem

#auth.ldap.ssl.cacertfile = etc/certs/cacert.pem

#auth.ldap.ssl.verify = verify_peer

#auth.ldap.ssl.fail_if_no_peer_cert = true

#auth.ldap.ssl.server_name_indication = your_server_name
