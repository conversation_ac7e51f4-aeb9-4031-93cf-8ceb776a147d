#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

### --------------------- User ---------------------
### 新增 User
#/auth/user/add
POST http://localhost:8300/auth/user/add
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "name": "dc3",
  "password": "dc3dc3dc3",
  "description": "平台开发者账号"
}


### 修改 User
#/auth/user/update
POST http://localhost:8300/auth/user/update
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "id": 1,
  "password": "dc3dc3dc3",
  "enable": true,
  "description": "平台开发者账号"
}


### 根据 ID 重置 User 密码
#/auth/user/reset/:id
POST http://localhost:8300/auth/user/reset/1
Accept: */*
Content-Type: application/json
Cache-Control: no-cache


### 根据 ID 查询 User
#/auth/user/id/:id
GET http://localhost:8300/auth/user/id/1
Accept: */*
Cache-Control: no-cache


### 根据 Name 查询 User
#/auth/user/name/:name
GET http://localhost:8300/auth/user/name/dc3
Accept: */*
Cache-Control: no-cache


### 分页查询 User
#/auth/user/list，支持name模糊查询
POST http://localhost:8300/auth/user/list
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "name": "",
  "page": {
    "current": 1,
    "size": 500,
    "orders": [
      {
        "column": "id",
        "asc": false
      }
    ]
  }
}


### 检测 User 是否存在并有效
#/auth/user/check/:name
GET http://localhost:8300/auth/user/check/dc3
Accept: */*
Cache-Control: no-cache


### 根据 ID 删除 User
#/auth/user/delete/:id
POST http://localhost:8300/auth/user/delete/1
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

###