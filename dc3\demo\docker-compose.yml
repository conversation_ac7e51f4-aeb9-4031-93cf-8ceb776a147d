#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

version: '3'

services:
  web:
    image: registry.cn-beijing.aliyuncs.com/dc3/iot-dc3-web:demo
    restart: always
    ports:
      - 80:80
      - 443:443
    container_name: dc3-web
    hostname: dc3-web
    volumes:
      - demo_nginx:/var/log/nginx
    networks:
      dc3demonet:
        aliases:
          - dc3-web

  gateway:
    image: registry.cn-beijing.aliyuncs.com/dc3/dc3-gateway:demo
    restart: always
    environment:
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-gateway
    hostname: dc3-gateway
    volumes:
      - demo_logs:/dc3-gateway/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3demonet:
        aliases:
          - dc3-gateway

  register:
    image: registry.cn-beijing.aliyuncs.com/dc3/dc3-center-register:demo
    restart: always
    environment:
      - EUREKA_DASHBOARD_PATH=/
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-center-register
    hostname: dc3-center-register
    volumes:
      - demo_logs:/dc3-center/dc3-center-register/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3demonet:
        aliases:
          - dc3-center-register

  auth:
    image: registry.cn-beijing.aliyuncs.com/dc3/dc3-center-auth:demo
    restart: always
    environment:
      - MYSQL_HOST=dc3-mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=dc3
      - REDIS_HOST=dc3-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dc3
      - CACHE_REDIS_TIME_TO_LIVE=6H
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-center-auth
    hostname: dc3-center-auth
    volumes:
      - demo_logs:/dc3-center/dc3-center-auth/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3demonet:
        aliases:
          - dc3-center-auth

  manager:
    image: registry.cn-beijing.aliyuncs.com/dc3/dc3-center-manager:demo
    restart: always
    environment:
      - MYSQL_HOST=dc3-mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=dc3
      - REDIS_HOST=dc3-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dc3
      - MONGO_HOST=dc3-mongo
      - MONGO_PORT=27017
      - MONGO_DATABASE=dc3
      - MONGO_AUTH_TYPE=password
      - MONGO_USERNAME=dc3
      - MONGO_PASSWORD=dc3
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - CACHE_REDIS_TIME_TO_LIVE=6H
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-center-manager
    hostname: dc3-center-manager
    volumes:
      - demo_logs:/dc3-center/dc3-center-manager/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3demonet:
        aliases:
          - dc3-center-manager

  data:
    image: registry.cn-beijing.aliyuncs.com/dc3/dc3-center-data:demo
    restart: always
    environment:
      - MYSQL_HOST=dc3-mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=dc3
      - REDIS_HOST=dc3-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dc3
      - MONGO_HOST=dc3-mongo
      - MONGO_PORT=27017
      - MONGO_DATABASE=dc3
      - MONGO_AUTH_TYPE=password
      - MONGO_USERNAME=dc3
      - MONGO_PASSWORD=dc3
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - CACHE_REDIS_TIME_TO_LIVE=6H
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-center-data
    hostname: dc3-center-data
    volumes:
      - demo_logs:/dc3-center/dc3-center-data/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3demonet:
        aliases:
          - dc3-center-data

  mysql:
    image: registry.cn-beijing.aliyuncs.com/dc3/dc3-mysql:demo
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=dc3
    container_name: dc3-mysql
    hostname: dc3-mysql
    volumes:
      - demo_mysql:/var/lib/mysql
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3demonet:
        aliases:
          - dc3-mysql

  mongo:
    image: registry.cn-beijing.aliyuncs.com/dc3/dc3-mongo:demo
    restart: always
    container_name: dc3-mongo
    hostname: dc3-mongo
    volumes:
      - demo_mongo_config:/data/configdb
      - demo_mongo_db:/data/db
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3demonet:
        aliases:
          - dc3-mongo

  redis:
    image: registry.cn-beijing.aliyuncs.com/dc3/dc3-redis:demo
    restart: always
    container_name: dc3-redis
    hostname: dc3-redis
    volumes:
      - demo_redis:/data
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3demonet:
        aliases:
          - dc3-redis

  rabbitmq:
    image: registry.cn-beijing.aliyuncs.com/dc3/dc3-rabbitmq:demo
    restart: always
    container_name: dc3-rabbitmq
    hostname: dc3-rabbitmq
    volumes:
      - demo_rabbitmq:/var/lib/rabbitmq
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3demonet:
        aliases:
          - dc3-rabbitmq

volumes:
  demo_nginx:
  demo_logs:
  demo_mysql:
  demo_mongo_config:
  demo_mongo_db:
  demo_redis:
  demo_rabbitmq:

networks:
  dc3demonet:
    driver: 'bridge'
...
