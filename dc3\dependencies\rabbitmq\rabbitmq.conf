#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# RabbitMQ Setting
loopback_users.guest = false
listeners.tcp.default = 5672
management.tcp.port = 15672
connection_max = 1024

default_pass = dc3
default_user = dc3
default_vhost = dc3

# Mqtt Setting
mqtt.vhost = dc3