#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

## Examples:
##auth.mnesia.1.login = admin
##auth.mnesia.1.password = public
##auth.mnesia.1.is_superuser = true
##auth.mnesia.2.login = <EMAIL>
##auth.mnesia.2.password = public
##auth.mnesia.2.is_superuser = false
##auth.mnesia.3.login = name~!@#$%^&*()_+
##auth.mnesia.3.password = pwsswd~!@#$%^&*()_+
##auth.mnesia.3.is_superuser = false

## Password hash.
##
## Value: plain | md5 | sha | sha256 
auth.mnesia.password_hash = plain

## Auth as username or auth as clientid.
##
## Value: username | clientid
auth.user.1.username = dc3
auth.user.1.password = dc3
