#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##====================================================================
## WebHook
##====================================================================

## The web services URL for Hook request
##
## Value: String
web.hook.api.url = http://127.0.0.1:8080

##--------------------------------------------------------------------
## HTTP Request Headers
##
## The header params what you extra need
## Format:
##    web.hook.headers.<param> = your-param
## Example:
## 1. web.hook.headers.token = your-token
## 2. web.hook.headers.other = others-param
##
## Value: String
## web.hook.headers.token = your-token

##--------------------------------------------------------------------
## Encode message payload field
##
## Value: base64 | base62
## web.hook.encode_payload = base64
## Mysql ssl configuration.
##
## Value: on | off
## web.hook.ssl = off

##--------------------------------------------------------------------
## CA certificate.
##
## Value: File
## web.hook.ssl.cafile  = path to your ca file
## Client ssl certificate.
##
## Value: File
## web.hook.ssl.certfile = path to your clientcert file

##--------------------------------------------------------------------
## Client ssl keyfile.
##
## Value: File
## web.hook.ssl.keyfile = path to your clientkey file

##--------------------------------------------------------------------
## Hook Rules
## These configuration items represent a list of events should be forwarded
##
## Format:
##   web.hook.rule.<HookName>.<No> = <Spec>
#web.hook.rule.client.connect.1       = {"action": "on_client_connect"}
#web.hook.rule.client.connack.1       = {"action": "on_client_connack"}
#web.hook.rule.client.connected.1     = {"action": "on_client_connected"}
#web.hook.rule.client.disconnected.1  = {"action": "on_client_disconnected"}
#web.hook.rule.client.subscribe.1     = {"action": "on_client_subscribe"}
#web.hook.rule.client.unsubscribe.1   = {"action": "on_client_unsubscribe"}
#web.hook.rule.session.subscribed.1   = {"action": "on_session_subscribed"}
#web.hook.rule.session.unsubscribed.1 = {"action": "on_session_unsubscribed"}
#web.hook.rule.session.terminated.1   = {"action": "on_session_terminated"}
#web.hook.rule.message.publish.1      = {"action": "on_message_publish"}
#web.hook.rule.message.delivered.1    = {"action": "on_message_delivered"}
#web.hook.rule.message.acked.1        = {"action": "on_message_acked"}
