#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

spring:
  security:
    user:
      name: ${SECURITY_USER_NAME:dc3}
      password: ${SECURITY_USER_PASSWORD:dc3}

eureka:
  instance:
    prefer-ip-address: true
    hostname: @project.artifactId@
    status-page-url-path: /actuator/info
    health-check-url-path: /actuator/health
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 15
  client:
    region: dc3
    availability-zones:
      dc3: defaultZone
    prefer-same-zone-eureka: true
    registry-fetch-interval-seconds: 5
    service-url:
      defaultZone: http://${spring.security.user.name}:${spring.security.user.password}@${EUREKA_HOST:dc3-center-register}:${EUREKA_PORT:8100}/eureka/