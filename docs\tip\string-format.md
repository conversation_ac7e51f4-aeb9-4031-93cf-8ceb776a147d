## String.format()字符串常规类型格式化的两种重载方式

- format(String format, Object… args) 新字符串使用本地语言环境，制定字符串格式和参数生成格式化的新字符串。
- format(Locale locale, String format, Object… args) 使用指定的语言环境，制定字符串格式和参数生成格式化的字符串。



## 转换符号说明

|转换符|详细说明|实例|
|------|-------|----|
|%s|字符串类型|“喜欢请收藏”|
|%c|字符类型|‘m’|
|%b|布尔类型|true|
|%d|整数类型（十进制）|88|
|%x|整数类型（十六进制）|FF|
|%o|整数类型（八进制）|77|
|%f|浮点类型|8.888|
|%a|十六进制浮点类型|FF.35AE|
|%e|指数类型|9.38e+5|
|%g|通用浮点类型（f和e类型中较短的）|	不举例(基本用不到)|
|%h|散列码|不举例(基本用不到)|
|%%|百分比类型|％(%特殊字符%%才能显示%)|
|%n|换行符|不举例(基本用不到)|
|%tx|日期与时间类型（x代表不同的日期与时间转换符)|不举例(基本用不到)|



## 转换符搭配说明

|标志|详细说明|实例|结果|
|----|-------|----|----|
|+|为正数或者负数添加符号|("%+d",15)|+15|
|-|左对齐|("%-5d",15)|15--|
|0|数字前面补0|("%04d", 99)|0099|
|空格|在整数之前添加指定数量的空格|("% 4d", 99)|--99|
|,|以“,”对数字分组|("%,f", 9999.99)|9,999.990000|
|(|使用括号包含负数|("%(f", -99.99)|(99.990000)|
|#|如果是浮点数则包含小数点，如果是16进制或8进制则添加x或o|("%#x", 99)/("%#o", 99)|0x63/0143|
|<|格式化前一个转换符所描述的参数|("%f和%<3.2f", 99.45)|99.450000和99.45|
|$|被格式化的参数索引|("%1$d,%2$s",99,"abc")|99,abc|


