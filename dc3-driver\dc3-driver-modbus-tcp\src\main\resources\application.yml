#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

driver:
  name: ModbusDriver
  type: driver
  project: @project.artifactId@
  description: @project.description@
  schedule:
    status:
      enable: true
      corn: '0/10 * * * * ?'
    read:
      enable: true
      corn: '0/30 * * * * ?'
    custom:
      enable: true
      corn:  '0/5 * * * * ?'
  driver-attribute:
    - displayName: 主机
      name: host
      type: string
      value: localhost
      description: Modbus IP
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 端口
      name: port
      type: int
      value: 502
      description: Modbus Port
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
  point-attribute:
    - displayName: 从站编号
      name: slaveId
      type: int
      value: 1
      description: 从站编号
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 功能码
      name: functionCode
      type: int
      value: 1
      description: 功能码 [1、2、3、4]
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 偏移量
      name: offset
      type: int
      value: 0
      description: 偏移量
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString

server:
  port: 8604

spring:
  profiles:
    active:
      - server
      - register
      - monitor
      - rabbitmq
      - quartz
      - driver
      - feign
      - ${NODE_ENV:dev}

logging:
  file:
    name: dc3/logs/driver/modbus-tcp/${spring.application.name}.log