#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Development environment, customizable configuration
eureka:
  instance:
    # Custom node registration IP
    ip-address: ${SERVICE_HOST:*************}

spring:
  datasource:
    # Customize MySQL configuration
    url: jdbc:mysql://${MYSQL_HOST:dc3-mysql}:${MYSQL_PORT:3306}/dc3?allowPublicKeyRetrieval=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai
  rabbitmq:
    # Customize Rabbitmq configuration
    host: ${RABBITMQ_HOST:dc3-rabbitmq}
    port: ${RABBITMQ_PORT:5672}
  redis:
    # Customize Redis configuration
    host: ${REDIS_HOST:dc3-redis}
    port: ${REDIS_PORT:6379}
  data:
    mongodb:
      # Customize Mongo configuration
      host: ${MONGO_HOST:dc3-mongo}
      port: ${MONGO_PORT:27017}
  cache:
    redis:
      # Customize Cache configuration
      time-to-live: ${CACHE_REDIS_TIME_TO_LIVE:5S}