[{"serviceName": "dc3-driver-modbus-tcp", "profiles": [{"name": "Modbus-Tcp-Profile", "share": false, "driverConfig": {"host": "127.0.0.1", "port": "502"}, "points": [{"name": "Modbus-Tcp-Point-String", "type": "string", "rw": 0, "base": 0, "minimum": -999999, "maximum": 999999, "multiple": 1, "accrue": false, "format": "%.3f", "unit": "-"}, {"name": "Modbus-Tcp-Point-Boolean", "type": "boolean", "rw": 0, "base": 0, "minimum": -999999, "maximum": 999999, "multiple": 1, "accrue": false, "format": "%.3f", "unit": "-"}, {"name": "Modbus-Tcp-Point-Int", "type": "int", "rw": 0, "base": 0, "minimum": -999999, "maximum": 999999, "multiple": 1, "accrue": false, "format": "%.3f", "unit": "-"}, {"name": "Modbus-Tcp-Point-Long", "type": "long", "rw": 0, "base": 0, "minimum": -999999, "maximum": 999999, "multiple": 1, "accrue": false, "format": "%.3f", "unit": "-"}, {"name": "Modbus-Tcp-Point-Float", "type": "float", "rw": 0, "base": 0, "minimum": -999999, "maximum": 999999, "multiple": 1, "accrue": false, "format": "%.3f", "unit": "-"}, {"name": "Modbus-Tcp-Point-Double", "type": "double", "rw": 0, "base": 0, "minimum": -999999, "maximum": 999999, "multiple": 1, "accrue": false, "format": "%.3f", "unit": "-"}], "groups": [{"name": "Modbus-Tcp-Group", "devices": [{"name": "Modbus-Tcp-Device", "multi": false, "pointConfig": {"Modbus-Tcp-Point-String": {"slaveId": "1", "functionCode": "3", "offset": "1"}, "Modbus-Tcp-Point-Boolean": {"slaveId": "1", "functionCode": "3", "offset": "2"}, "Modbus-Tcp-Point-Int": {"slaveId": "1", "functionCode": "3", "offset": "3"}, "Modbus-Tcp-Point-Long": {"slaveId": "1", "functionCode": "3", "offset": "4"}, "Modbus-Tcp-Point-Float": {"slaveId": "1", "functionCode": "3", "offset": "5"}, "Modbus-Tcp-Point-Double": {"slaveId": "1", "functionCode": "3", "offset": "6"}}}]}]}]}]