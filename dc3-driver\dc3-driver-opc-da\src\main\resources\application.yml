driver:
  name: OpcDaDriver
  type: driver
  project: @project.artifactId@
  description: @project.description@
  schedule:
    status:
      enable: true
      corn: '0/10 * * * * ?'
    read:
      enable: true
      corn: '0/30 * * * * ?'
    custom:
      enable: true
      corn:  '0/5 * * * * ?'
  driver-attribute:
    - displayName: 主机
      name: host
      type: string
      value: localhost
      description: Opc Da Host
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: CLSID
      name: clsId
      type: string
      value: F8582CF2-88FB-11D0-B850-00C0F0104305
      description: Opc Da Server CLAID
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 用户名
      name: username
      type: string
      value: dc3
      description: Opc Da UserName
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 密码
      name: password
      type: string
      value: dc3dc3
      description: Op<PERSON> Da Passward
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
  point-attribute:
    - displayName: 分组
      name: group
      type: string
      value: GROUP
      description: 分组名称
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 位号
      name: tag
      type: string
      value: TAG
      description: 位号名称
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString

server:
  port: 8602

spring:
  profiles:
    active:
      - server
      - register
      - monitor
      - rabbitmq
      - quartz
      - driver
      - feign
      - ${NODE_ENV:dev}

logging:
  file:
    name: dc3/logs/driver/opcda/${spring.application.name}.log