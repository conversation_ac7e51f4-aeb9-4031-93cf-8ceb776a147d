/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.openscada.opc.dcom.da;

public interface Constants extends org.openscada.opc.dcom.common.Constants {
    public static final String IOPCServer_IID = "39C13A4D-011E-11D0-9675-0020AFD8ADB3";

    public static final String IOPCGroupStateMgt_IID = "39C13A50-011E-11D0-9675-0020AFD8ADB3";

    public static final String IOPCBrowse_IID = "39227004-A18F-4B57-8B0A-5235670F4468";

    public static final String IOPCBrowseServerAddressSpace_IID = "39C13A4F-011E-11D0-9675-0020AFD8ADB3";

    public static final String IOPCItemMgt_IID = "39C13A54-011E-11D0-9675-0020AFD8ADB3";

    public static final String IOPCItemProperties_IID = "39C13A72-011E-11D0-9675-0020AFD8ADB3";

    public static final String IOPCItemIO_IID = "85C0B427-2893-4CBC-BD78-E5FC5146F08F";

    public static final String IOPCDataCallback_IID = "39C13A70-011E-11D0-9675-0020AFD8ADB3";

    public static final String IOPCAsyncIO2_IID = "39C13A71-011E-11D0-9675-0020AFD8ADB3";

    public static final String IOPCSyncIO_IID = "39C13A52-011E-11D0-9675-0020AFD8ADB3";
}
