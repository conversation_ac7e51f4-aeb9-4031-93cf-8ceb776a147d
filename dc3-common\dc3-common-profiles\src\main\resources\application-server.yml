#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

server:
  undertow:
    threads:
      io: 2
      worker: 20
    buffer-size: 512
  thread:
    prefix: dc3-thread-
    core-pool-size: 4
    maximum-pool-size: 32
    keep-alive-time: 15

spring:
  transaction:
    rollback-on-commit-failure: true

logging:
  pattern:
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } --- [%t] [%4line] %-40.40logger{39} : %m%n%wEx'
    console: '%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} [%clr(%4line){magenta}] %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx'
  logback:
    rollingpolicy:
      max-history: 30
      total-size-cap: 2GB
      clean-history-on-start: true
