#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

driver:
  name: PlcS7Driver
  type: driver
  project: @project.artifactId@
  description: @project.description@
  schedule:
    status:
      enable: true
      corn: '0/10 * * * * ?'
    read:
      enable: true
      corn: '0/30 * * * * ?'
    custom:
      enable: true
      corn:  '0/5 * * * * ?'
  driver-attribute:
    - displayName: 主机
      name: host
      type: string
      value: ************
      description: Ip
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 端口
      name: port
      type: int
      value: 102
      description: Port
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
  point-attribute:
    - displayName: DB序号
      name: dbNum
      type: int
      value: 0
      description: 数据块号，从 0 开始计数
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 字偏移
      name: byteOffset
      type: int
      value: 0
      description: 字偏移
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 位偏移
      name: bitOffset
      type: int
      value: 0
      description: 位偏移
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 数据块长度
      name: blockSize
      type: int
      value: 8
      description: 数据块长度
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString

server:
  port: 8601

spring:
  profiles:
    active:
      - server
      - register
      - monitor
      - rabbitmq
      - quartz
      - driver
      - feign
      - ${NODE_ENV:dev}

logging:
  file:
    name: dc3/logs/driver/plcs7/${spring.application.name}.log