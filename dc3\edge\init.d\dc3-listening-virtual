#! /bin/bash

#
# Copyright 2022 Pnoker All Rights Reserved
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

### BEGIN INIT INFO
# Provides:             listening-virtual
# Required-Start:       $local_fs $network
# Required-Stop:        $local_fs $network
# Default-Start:        2 3 4 5
# Default-Stop:         0 1 6
# Short-Description:    DC3 Listening Virtual Driver
# Description:          DC3 Listening Virtual Driver Service Daemon
### END INIT INFO

set -e

JAR_FILE="dc3-driver-listening-virtual.jar"
BASE_DIR="/home/<USER>/iot-dc3/dc3-driver/dc3-driver-listening-virtual/target"
JAVA_OPS="-server -Xms512m -Xmx1024m -XX:MetaspaceSize=200m -XX:MaxMetaspaceSize=200m"

start()
{
export JAVA_HOME=/usr/local/java/jdk1.8.0_251
export PATH=$JAVA_HOME/bin:$PATH
export CLASSPATH=.:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib

cd $BASE_DIR
nohup java $JAVA_OPS -Djava.security.egd=file:/dev/./urandom -jar $JAR_FILE >/dev/null &
}

stop()
{
pid=`ps -ef|grep $JAR_FILE |grep -v grep |awk '{print $2}'`
kill -9 $pid
}

case "$1" in

start)
start
;;

stop)
stop
;;

restart)
stop
start
;;

*)
esac
exit 0