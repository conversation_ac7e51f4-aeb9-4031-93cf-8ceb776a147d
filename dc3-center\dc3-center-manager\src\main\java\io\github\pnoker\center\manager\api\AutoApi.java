/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.github.pnoker.center.manager.api;

import cn.hutool.core.util.ObjectUtil;
import io.github.pnoker.api.center.manager.feign.AutoClient;
import io.github.pnoker.center.manager.service.AutoService;
import io.github.pnoker.common.bean.R;
import io.github.pnoker.common.bean.point.PointDetail;
import io.github.pnoker.common.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 自发现 Client 接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(ServiceConstant.Manager.AUTO_URL_PREFIX)
public class AutoApi implements AutoClient {

    @Resource
    private AutoService autoService;

    @Override
    public R<PointDetail> autoCreateDeviceAndPoint(PointDetail pointDetail, String tenantId) {
        try {
            PointDetail createDeviceAndPoint = autoService.autoCreateDeviceAndPoint(pointDetail.getDeviceName(), pointDetail.getPointName(), pointDetail.getDriverId(), tenantId);
            if (ObjectUtil.isNotNull(createDeviceAndPoint)) {
                return R.ok(createDeviceAndPoint);
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return R.fail();
    }
}
