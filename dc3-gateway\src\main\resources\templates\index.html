<!--
  ~ Copyright 2022 Pnoker All Rights Reserved
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="IOT-DC3 可分布式的物联网(IOT)平台 , 基于开源Spring Cloud框架搭建，实现设备互联、管理、数据采集、代码生成、快速开发和可视化等，兼容多种流行的物联网数据接入协议（Mqtt\Hart\Modbus\Wia\Rtsp\Rtmp\Siemens PLC S7...），是一整套完整的物联系统解决方案。">
    <meta name="author" content="pnoker">

    <title>DC3 Gateway</title>
    <!-- Additional CSS Files -->
    <link rel="stylesheet" type="text/css" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="assets/css/font-awesome.css">
    <link rel="stylesheet" type="text/css" href="assets/css/templatemo-art-factory.css">
    <link rel="stylesheet" type="text/css" href="assets/css/owl-carousel.css">

</head>

<body ondragstart="return false" onselectstart="return false">

<!-- ***** Preloader Start ***** -->
<div id="preloader">
    <div class="jumper">
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>
<!-- ***** Preloader End ***** -->

<!-- ***** Header Area Start ***** -->
<header class="header-area header-sticky">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <nav class="main-nav">
                    <!-- ***** Logo Start ***** -->
                    <a href="#" class="logo">物联万物，智控未来</a>
                    <!-- ***** Logo End ***** -->
                </nav>
            </div>
        </div>
    </div>
</header>
<!-- ***** Header Area End ***** -->

<!-- ***** Welcome Area Start ***** -->
<div class="welcome-area" id="welcome">

    <!-- ***** Header Text Start ***** -->
    <div class="header-text">
        <div class="container">
            <div class="row">
                <div class="left-text col-lg-6 col-md-6 col-sm-12 col-xs-12" data-scroll-reveal="enter left move 30px over 0.6s after 0.4s">
                    <h1>IOT DC3</h1>
                    <p>DC3是基于Spring Cloud的开源可分布式物联网(IOT)平台,用于快速开发、部署物联设备接入项目,是一整套物联系统解决方案。</p>
                    <a href="https://doc.dc3.site" class="main-button-slider">了解更多</a>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12" data-scroll-reveal="enter right move 30px over 0.6s after 0.4s">
                    <img src="assets/images/slider-icon.png" class="rounded img-fluid d-block mx-auto" alt="First Vector Graphic">
                </div>
            </div>
        </div>
    </div>
    <!-- ***** Header Text End ***** -->
</div>
<!-- ***** Welcome Area End ***** -->

<!-- ***** Features Big Item Start ***** -->
<section class="section" id="about2">
    <div class="container">
        <div class="row">

        </div>
    </div>
</section>
<!-- ***** Features Big Item End ***** -->

<!-- ***** Features Small Start ***** -->
<section class="section" id="services">
    <div class="container">
        <div class="row">
            <div class="owl-carousel owl-theme">
                <div class="item service-item">
                    <div class="icon">
                        <i><img src="assets/images/service-icon-01.png" alt=""></i>
                    </div>
                    <h5 class="service-title">服务注册中心</h5>
                </div>
                <div class="item service-item">
                    <div class="icon">
                        <i><img src="assets/images/service-icon-02.png" alt=""></i>
                    </div>
                    <h5 class="service-title">服务监控中心</h5>
                </div>
                <div class="item service-item">
                    <div class="icon">
                        <i><img src="assets/images/service-icon-03.png" alt=""></i>
                    </div>
                    <h5 class="service-title">镜像集合</h5>
                </div>
                <div class="item service-item">
                    <div class="icon">
                        <i><img src="assets/images/service-icon-02.png" alt=""></i>
                    </div>
                    <h5 class="service-title">平台数据中心</h5>
                </div>
                <div class="item service-item">
                    <div class="icon">
                        <i><img src="assets/images/service-icon-01.png" alt=""></i>
                    </div>
                    <h5 class="service-title">视频转码服务</h5>
                </div>
                <div class="item service-item">
                    <div class="icon">
                        <i><img src="assets/images/service-icon-03.png" alt=""></i>
                    </div>
                    <h5 class="service-title">OPC服务</h5>
                </div>
                <div class="item service-item">
                    <div class="icon">
                        <i><img src="assets/images/service-icon-01.png" alt=""></i>
                    </div>
                    <h5 class="service-title">OPC UA服务</h5>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- ***** Features Small End ***** -->

<!-- jQuery -->
<script src="assets/js/jquery-2.1.0.min.js"></script>

<!-- Bootstrap -->
<script src="assets/js/popper.js"></script>
<script src="assets/js/bootstrap.min.js"></script>

<!-- Plugins -->
<script src="assets/js/owl-carousel.js"></script>
<script src="assets/js/scrollreveal.min.js"></script>
<script src="assets/js/waypoints.min.js"></script>
<script src="assets/js/jquery.counterup.min.js"></script>
<script src="assets/js/imgfix.min.js"></script>

<!-- Global Init -->
<script src="assets/js/custom.js"></script>

</body>
</html>