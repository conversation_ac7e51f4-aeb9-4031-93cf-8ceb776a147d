#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## Telemetry
##--------------------------------------------------------------------

## Enable telemetry
##
## Value: true | false
##
## Default: true
telemetry.enabled = true

## The destination URL for the telemetry data report
##
## Value: String
##
## Default: https://telemetry.emqx.io/api/telemetry
telemetry.url = https://telemetry.emqx.io/api/telemetry

## Interval for reporting telemetry data
##
## Value: Duration
## -d: day
## -h: hour
## -m: minute
## -s: second
##
## Default: 7d
telemetry.report_interval = 7d