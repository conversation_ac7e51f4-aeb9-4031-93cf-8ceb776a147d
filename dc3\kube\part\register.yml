#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-center-register
  namespace: dc3
  labels:
    dc3.version: 2022.1.0
    dc3.service: dc3-center-register
spec:
  ports:
    - name: "8100"
      port: 8100
      targetPort: 8100
  selector:
    dc3.service: dc3-center-register

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-center-register
  namespace: dc3
  labels:
    dc3.service: dc3-center-register
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-center-register
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-center-register
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-center-register
      volumes:
        - name: dc3-storage
          persistentVolumeClaim:
            claimName: dc3-persistent-volume-claim
      containers:
        - name: dc3-center-register
          image: pnoker/dc3-center-register:1.0.0
          ports:
            - containerPort: 8100
          env:
            - name: NODE_ENV
              value: "test"
            - name: EUREKA_DASHBOARD_PATH
              value: "/dc3/register"
          resources:
            requests:
              cpu: "100m"
              memory: "200Mi"
            limits:
              cpu: "1000m"
              memory: "2000Mi"
          volumeMounts:
            - mountPath: "/dc3-center/dc3-center-register/dc3/logs"
              name: dc3-storage