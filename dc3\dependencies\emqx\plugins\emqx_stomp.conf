#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## Stomp Plugin
##--------------------------------------------------------------------

##--------------------------------------------------------------------
## Stomp listener

## The Port that stomp listener will bind.
##
## Value: Port
stomp.listener = 61613

## The acceptor pool for stomp listener.
##
## Value: Number
stomp.listener.acceptors = 4

## Maximum number of concurrent stomp connections.
##
## Value: Number
stomp.listener.max_connections = 512

## Whether to enable SSL.
##
## Value: on | off
## stomp.listener.ssl = off

## Path to the file containing the user's private PEM-encoded key.
##
## Value: File
## stomp.listener.keyfile = etc/certs/key.pem

## Path to a file containing the user certificate.
##
## Value: File
## stomp.listener.certfile = etc/certs/cert.pem

## Path to the file containing PEM-encoded CA certificates.
##
## Value: File
## stomp.listener.cacertfile = etc/certs/cacert.pem

## See: 'listener.ssl.<name>.dhfile' in emq.conf
##
## Value: File
## stomp.listener.dhfile = etc/certs/dh-params.pem

## See: 'listener.ssl.<name>.vefify' in emq.conf
##
## Value: vefify_peer | verify_none
## stomp.listener.verify = verify_peer

## See: 'listener.ssl.<name>.fail_if_no_peer_cert' in emq.conf
##
## Value: false | true
## stomp.listener.fail_if_no_peer_cert = true

## TLS versions only to protect from POODLE attack.
##
## Value: String, seperated by ','
## stomp.listener.tls_versions = tlsv1.2,tlsv1.1,tlsv1

## SSL Handshake timeout.
##
## Value: Duration
## stomp.listener.handshake_timeout = 15s

## See: 'listener.ssl.<name>.ciphers' in emq.conf
##
## Value: Ciphers
## stomp.listener.ciphers = ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-ECDSA-AES256-SHA384,ECDHE-RSA-AES256-SHA384,ECDHE-ECDSA-DES-CBC3-SHA,ECDH-ECDSA-AES256-GCM-SHA384,ECDH-RSA-AES256-GCM-SHA384,ECDH-ECDSA-AES256-SHA384,ECDH-RSA-AES256-SHA384,DHE-DSS-AES256-GCM-SHA384,DHE-DSS-AES256-SHA256,AES256-GCM-SHA384,AES256-SHA256,ECDHE-ECDSA-AES128-GCM-SHA256,ECDHE-RSA-AES128-GCM-SHA256,ECDHE-ECDSA-AES128-SHA256,ECDHE-RSA-AES128-SHA256,ECDH-ECDSA-AES128-GCM-SHA256,ECDH-RSA-AES128-GCM-SHA256,ECDH-ECDSA-AES128-SHA256,ECDH-RSA-AES128-SHA256,DHE-DSS-AES128-GCM-SHA256,DHE-DSS-AES128-SHA256,AES128-GCM-SHA256,AES128-SHA256,ECDHE-ECDSA-AES256-SHA,ECDHE-RSA-AES256-SHA,DHE-DSS-AES256-SHA,ECDH-ECDSA-AES256-SHA,ECDH-RSA-AES256-SHA,AES256-SHA,ECDHE-ECDSA-AES128-SHA,ECDHE-RSA-AES128-SHA,DHE-DSS-AES128-SHA,ECDH-ECDSA-AES128-SHA,ECDH-RSA-AES128-SHA,AES128-SHA

## See: 'listener.ssl.<name>.secure_renegotiate' in emq.conf
##
## Value: on | off
## stomp.listener.secure_renegotiate = off

## See: 'listener.ssl.<name>.reuse_sessions' in emq.conf
##
## Value: on | off
## stomp.listener.reuse_sessions = on

## See: 'listener.ssl.<name>.honor_cipher_order' in emq.conf
##
## Value: on | off
## stomp.listener.honor_cipher_order = on

##--------------------------------------------------------------------
## Stomp login user and password

## Default login user
##
## Value: String
stomp.default_user.login = guest

## Default login password
##
## Value: String
stomp.default_user.passcode = guest

## Allow anonymous authentication.
##
## Value: true | false
stomp.allow_anonymous = true

##--------------------------------------------------------------------
## Stomp frame

## Maximum numbers of frame headers.
##
## Value: Number
stomp.frame.max_headers = 10

## Maximum length of frame header.
##
## Value: Number
stomp.frame.max_header_length = 1024

## Maximum body length of frame.
##
## Value: Number
stomp.frame.max_body_length = 8192

