/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.openscada.opc.lib.da;

public class UnknownGroupException extends Exception {
    private String _name = null;

    public UnknownGroupException(final String name) {
        super();
        this._name = name;
    }

    /**
     *
     */
    private static final long serialVersionUID = 1771564928794033075L;

    public String getName() {
        return this._name;
    }

    public void setName(final String name) {
        this._name = name;
    }

}
