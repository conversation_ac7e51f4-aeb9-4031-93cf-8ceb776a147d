/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.github.pnoker.driver.api;

/**
 * <AUTHOR>
 * Create on 2/27/19.
 * @version 1.0
 */
public enum SiemensPLCS {

    /**
     * S200
     */
    S200,

    /**
     * S200Smart
     */
    S200Smart,

    /**
     * except the 200 series
     */
    SNon200,

    /**
     * S300
     */
    S300,

    /**
     * S400
     */
    S400,

    /**
     * S1200
     */
    S1200,

    /**
     * S1500
     */
    S1500,

}
