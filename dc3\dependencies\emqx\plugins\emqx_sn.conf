#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## MQTT-SN
##--------------------------------------------------------------------

## The UDP port which emq-sn is listening on.
##
## Value: IP:Port | Port
##
## Examples: 1884, 127.0.0.1:1884, ::1:1884
mqtt.sn.port = 1884

## The duration that emqx-sn broadcast ADVERTISE message through.
##
## Value: Duration
mqtt.sn.advertise_duration = 15m

## The MQTT-SN Gateway id in ADVERTISE message.
##
## Value: Number
mqtt.sn.gateway_id = 1

## To control whether write statistics data into ETS table for dashbord to read.
##
## Value: on | off
mqtt.sn.enable_stats = off

## To control whether accept and process the received publish message with qos=-1.
##
## Value: on | off
mqtt.sn.enable_qos3 = off

## MQTT SN idle timeout, specified in seconds.
##
## Value: Duration
mqtt.sn.idle_timeout = 30s

## The pre-defined topic name corresponding to the pre-defined topic id of N.
## Note that the pre-defined topic id of 0 is reserved.
mqtt.sn.predefined.topic.0 = reserved
mqtt.sn.predefined.topic.1 = /predefined/topic/name/hello
mqtt.sn.predefined.topic.2 = /predefined/topic/name/nice

## Default username for MQTT-SN. This parameter is optional. If specified,
## emq-sn will connect EMQ core with this username. It is useful if any auth
## plug-in is enabled.
##
## Value: String
mqtt.sn.username = mqtt_sn_user

## This parameter is optional. Pair with username above.
##
## Value: String
mqtt.sn.password = abc
