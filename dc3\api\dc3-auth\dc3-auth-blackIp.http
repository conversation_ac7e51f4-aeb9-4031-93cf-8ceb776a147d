#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

### --------------------- BlackIp ---------------------
### 新增 BlackIp
#/auth/black_ip/add
POST http://localhost:8300/auth/black_ip/add
Accept: */*
Content-Type: application/json
Cache-Control: no-cache
X-Auth-Tenant-Id: -1

{
  "ip": "127.0.0.1",
  "description": "测试黑名单Ip"
}


### 修改 BlackIp
#/auth/black_ip/update
POST http://localhost:8300/auth/black_ip/update
Accept: */*
Content-Type: application/json
Cache-Control: no-cache
X-Auth-Tenant-Id: -1

{
  "id": 1,
  "ip": "***********",
  "enable": true,
  "description": "测试黑名单Ip"
}


### 根据 ID 查询 BlackIp
#/auth/black_ip/id/:id
GET http://localhost:8300/auth/black_ip/id/1
Accept: */*
Cache-Control: no-cache


### 根据 Ip 查询 BlackIp
#/auth/user/ip/:ip
GET http://localhost:8300/auth/black_ip/ip/127.0.0.1
Accept: */*
Cache-Control: no-cache
X-Auth-Tenant-Id:-1


### 分页查询 BlackIp
#/auth/black_ip/list，支持ip模糊查询
POST http://localhost:8300/auth/black_ip/list
Accept: */*
Content-Type: application/json
Cache-Control: no-cache
X-Auth-Tenant-Id:-1

{
  "ip": "",
  "page": {
    "current": 1,
    "size": 500,
    "orders": [
      {
        "column": "id",
        "asc": false
      }
    ]
  }
}


### 查询 BlackIp 是否存在并有效
#/auth/black_ip/check/:ip
GET http://localhost:8300/auth/black_ip/check/***********
Accept: */*
Cache-Control: no-cache
X-Auth-Tenant-Id:-1


### 根据 ID 删除 BlackIp
#/auth/black_ip/delete/:id
POST http://localhost:8300/auth/black_ip/delete/1383257757074976769
Accept: */*
Content-Type: application/json
Cache-Control: no-cache