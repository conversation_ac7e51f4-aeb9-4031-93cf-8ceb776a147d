#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## HTTP Auth/ACL Plugin
##--------------------------------------------------------------------

##--------------------------------------------------------------------
## Authentication request.

## HTTP URL API path for authentication request
##
## Value: URL
##
## Examples: http://127.0.0.1:8991/mqtt/auth, https://[::1]:8991/mqtt/auth
auth.http.auth_req = http://127.0.0.1:8991/mqtt/auth

## Value: post | get | put
auth.http.auth_req.method = post

## It only works when method=post
## Value: json | x-www-form-urlencoded
auth.http.auth_req.content_type = x-www-form-urlencoded

## Variables:
##  - %u: username
##  - %c: clientid
##  - %a: ipaddress
##  - %r: protocol
##  - %P: password
##  - %p: sockport of server accepted
##  - %C: common name of client TLS cert
##  - %d: subject of client TLS cert
##
## Value: Params
auth.http.auth_req.params = clientid=%c,username=%u,password=%P

##--------------------------------------------------------------------
## Superuser request.

## HTTP URL API path for Superuser request
##
## Value: URL
##
## Examples: http://127.0.0.1:8991/mqtt/superuser, https://[::1]:8991/mqtt/superuser
#auth.http.super_req = http://127.0.0.1:8991/mqtt/superuser

## Value: post | get | put
#auth.http.super_req.method = post

## It only works when method=pos
## Value: json | x-www-form-urlencoded
#auth.http.super_req.content_type = x-www-form-urlencoded

## Variables:
##  - %u: username
##  - %c: clientid
##  - %a: ipaddress
##  - %r: protocol
##  - %P: password
##  - %p: sockport of server accepted
##  - %C: common name of client TLS cert
##  - %d: subject of client TLS cert
##
## Value: Params
#auth.http.super_req.params = clientid=%c,username=%u

##--------------------------------------------------------------------
## ACL request.

## HTTP URL API path for ACL request
##
## Value: URL
##
## Examples: http://127.0.0.1:8991/mqtt/acl, https://[::1]:8991/mqtt/acl
auth.http.acl_req = http://127.0.0.1:8991/mqtt/acl

## Value: post | get | put
auth.http.acl_req.method = get

## It only works when method=post
## Value: json | x-www-form-urlencoded
auth.http.acl_req.content_type = x-www-form-urlencoded

## Variables:
##  - %A: 1 | 2, 1 = sub, 2 = pub
##  - %u: username
##  - %c: clientid
##  - %a: ipaddress
##  - %r: protocol
##  - %m: mountpoint
##  - %t: topic
##
## Value: Params
auth.http.acl_req.params = access=%A,username=%u,clientid=%c,ipaddr=%a,topic=%t,mountpoint=%m

##------------------------------------------------------------------------------
## Http Reqeust options

## Time-out time for the http request, 0 is never timeout.
##
## Value: Duration
## -h: hour, e.g. '2h' for 2 hours
## -m: minute, e.g. '5m' for 5 minutes
## -s: second, e.g. '30s' for 30 seconds
##
## Default: 0
## auth.http.request.timeout = 0

## Connection time-out time, used during the initial request
## when the client is connecting to the server
##
## Value: Duration
##
## Default is same with the timeout option
## auth.http.request.connect_timeout = 0

## Re-send http reuqest times
##
## Value: integer
##
## Default: 3
auth.http.request.retry_times = 3

## The interval for re-sending the http request
##
## Value: Duration
##
## Default: 1s
auth.http.request.retry_interval = 1s

## The 'Exponential Backoff' mechanism for re-sending request. The actually
## re-send time interval is `interval * backoff ^ times`
##
## Value: float
##
## Default: 2.0
auth.http.request.retry_backoff = 2.0

##------------------------------------------------------------------------------
## SSL options

## Path to the file containing PEM-encoded CA certificates. The CA certificates
## are used during server authentication and when building the client certificate chain.
##
## Value: File
## auth.http.ssl.cacertfile = etc/certs/ca.pem

## The path to a file containing the client's certificate.
##
## Value: File
## auth.http.ssl.certfile = etc/certs/client-cert.pem

## Path to a file containing the client's private PEM-encoded key.
##
## Value: File
## auth.http.ssl.keyfile = etc/certs/client-key.pem

##--------------------------------------------------------------------
## HTTP Request Headers
##
## Example: auth.http.header.Accept-Encoding = *
##
## Value: String
## auth.http.header.Accept = */*