#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

### --------------------- Driver Attribute ---------------------
### 新增 DriverAttribute
#/manager/driver_attribute/add
POST http://localhost:8400/manager/driver_attribute/add
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "displayName": "测试IP",
  "name": "ip",
  "type": "string",
  "value": "localhost",
  "driverId": -1,
  "description": "测试驱动属性"
}


### 修改 DriverAttribute
#/manager/driver_attribute/update
POST http://localhost:8400/manager/driver_attribute/update
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "id": 1,
  "displayName": "测试IP",
  "name": "ip",
  "type": "string",
  "value": "localhost",
  "driverId": -1,
  "description": "测试驱动属性"
}


### 根据 ID 查询 DriverAttribute
#/manager/driver_attribute/id/:id
GET http://localhost:8400/manager/driver_attribute/id/1
Accept: */*
Cache-Control: no-cache



### 分页查询 DriverAttribute
#/manager/driver_attribute/list，支持displayName模糊查询，name\type\driverId精准查询
POST http://localhost:8400/manager/driver_attribute/list
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "displayName": "",
  "page": {
    "current": 1,
    "orders": [
      {
        "column": "id",
        "asc": false
      }
    ]
  }
}


### 根据 ID 删除 DriverAttribute
#/manager/driver_attribute/delete/:id
POST http://localhost:8400/manager/driver_attribute/delete/1
Accept: */*
Content-Type: application/json
Cache-Control: no-cache