<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2022 Pnoker All Rights Reserved
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>io.github.pnoker</groupId>
    <artifactId>iot-dc3</artifactId>
    <version>2022.1.0</version>
    <packaging>pom</packaging>

    <developers>
        <developer>
            <id>pnoker</id>
            <name>pnoker</name>
            <email><EMAIL></email>
            <organization>pnoker.github.io</organization>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <name>${project.artifactId}</name>
    <url>https://github.com/pnoker/iot-dc3</url>
    <inceptionYear>2017-2020</inceptionYear>
    <description>
        IOT-DC3 可分布式的物联网(IOT)平台 , 基于开源Spring Cloud框架搭建，实现设备互联、管理、数据采集、代码生成、快速开发和可视化等，
        兼容多种流行的物联网数据接入协议（Mqtt\Socket\Modbus-Tcp\Rtsp\Rtmp\Siemens PLC S7\OpcUa\OpcDa...），是一整套完整的物联系统解决方案。
    </description>

    <modules>
        <module>dc3-gateway</module>
        <module>dc3-center</module>

        <module>dc3-transfer</module>
        <module>dc3-driver</module>

        <module>dc3-api</module>
        <module>dc3-common</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- Spring 相关-->
        <spring-boot.version>2.7.0</spring-boot.version>
        <spring-cloud.version>2021.0.3</spring-cloud.version>
        <spring-platform.version>Cairo-SR8</spring-platform.version>

        <!-- 其他依赖-->
        <spring-boot-admin.version>2.7.0</spring-boot-admin.version>
        <mybatis.plus.version>3.5.2</mybatis.plus.version>
        <jwt.version>0.9.1</jwt.version>
        <jna.version>5.11.0</jna.version>
        <oshi.version>6.1.6</oshi.version>
        <jaxb.api.version>2.3.1</jaxb.api.version>
        <jaxb.version>3.0.2</jaxb.version>
        <activation.version>1.1.1</activation.version>
        <milo.version>0.6.6</milo.version>
        <modbus.version>3.0.4</modbus.version>
        <netty.version>4.1.76.Final</netty.version>
        <j.interop.version>2.0.4</j.interop.version>
        <bouncycastle.version>1.46</bouncycastle.version>
        <paho.mqttv3.version>1.2.5</paho.mqttv3.version>
        <elasticsearch.version>7.17.4</elasticsearch.version>
        <jakarta.version>2.0.1</jakarta.version>
        <guava.version>31.1-jre</guava.version>
        <hutool.version>5.8.3</hutool.version>

        <!-- Plugin 相关 -->
        <maven.source.plugin>3.2.1</maven.source.plugin>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.plugin>3.8.0</maven.compiler.plugin>
        <maven.resource.plugin>3.1.0</maven.resource.plugin>
        <spring.boot.maven.plugin>2.2.2.RELEASE</spring.boot.maven.plugin>
    </properties>

    <dependencies>

        <!-- Spring Configuration-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <scope>compile</scope>
            <optional>true</optional>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Cloud 依赖 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.spring.platform</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${spring-platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Admin -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- Mybatis Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>

            <!-- Jwt -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- Oshi & Jna -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <!-- Jaxb Api -->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>${activation.version}</version>
            </dependency>

            <!-- Eclipse Milo SDK Client -->
            <dependency>
                <groupId>org.eclipse.milo</groupId>
                <artifactId>sdk-client</artifactId>
                <version>${milo.version}</version>
            </dependency>

            <!-- Modbus -->
            <dependency>
                <groupId>com.infiniteautomation</groupId>
                <artifactId>modbus4j</artifactId>
                <version>${modbus.version}</version>
            </dependency>

            <!--Netty-->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <!-- Java Interop 相关 -->
            <dependency>
                <groupId>org.jinterop</groupId>
                <artifactId>j-interop</artifactId>
                <version>${j.interop.version}</version>
            </dependency>

            <!-- Tls Cert 相关 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk16</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <!-- Elasticsearch 相关 -->
            <dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.json</groupId>
                <artifactId>jakarta.json-api</artifactId>
                <version>${jakarta.version}</version>
            </dependency>

            <!-- Mqtt 相关 -->
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${paho.mqttv3.version}</version>
            </dependency>

            <!-- Guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- Hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- Dc3 Api -->
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-api-auth</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-api-manager</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-api-data</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-api-rtmp</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-api-driver</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>

            <!-- Dc3 Driver Sdk  -->
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-common-sdk</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>

            <!-- Dc3 Common -->
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-common-api</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-common-core</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-common-base</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.github.pnoker</groupId>
                <artifactId>dc3-common-profiles</artifactId>
                <version>${project.version}</version>
                <scope>compile</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>github</id>
            <name>GitHub DC3 Apache Maven Packages</name>
            <url>https://maven.pkg.github.com/pnoker/iot-dc3</url>
        </repository>
    </distributionManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>spring-plugin</id>
            <name>Aliyun Spring Plugin Repository</name>
            <url>https://maven.aliyun.com/repository/spring-plugin</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <repositories>
        <repository>
            <id>public</id>
            <name>Aliyun Apache Maven Public Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring</id>
            <name>Aliyun Spring Repository</name>
            <url>https://maven.aliyun.com/repository/spring</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <NODE_ENV>dev</NODE_ENV>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <NODE_ENV>test</NODE_ENV>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <NODE_ENV>prod</NODE_ENV>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.maven.plugin}</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- 编译相关 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven.compiler.plugin}</version>
                    <configuration>
                        <target>${maven.compiler.target}</target>
                        <source>${maven.compiler.source}</source>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <!-- 源码相关 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven.source.plugin}</version>
                    <executions>
                        <execution>
                            <id>sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- 过滤文件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven.resource.plugin}</version>
                    <configuration>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>ico</nonFilteredFileExtension>
                            <nonFilteredFileExtension>db</nonFilteredFileExtension>
                            <nonFilteredFileExtension>key</nonFilteredFileExtension>
                            <nonFilteredFileExtension>crt</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
