<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2022 Pnoker All Rights Reserved
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE configuration>
<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <logger name="DriverServiceImpl" level="WARN"/>
    <logger name="org.springframework.boot.autoconfigure.thymeleaf" level="ERROR"/>
    <logger name="org.springframework.boot.web.embedded.undertow" level="WARN"/>
    <logger name="org.springframework.cloud.netflix.eureka" level="WARN"/>
    <logger name="org.springframework.cloud.gateway.route" level="WARN"/>
    <logger name="org.springframework.cloud.commons.util" level="ERROR"/>
    <logger name="io.github.pnoker.center.manager.service.rabbit" level="WARN"/>
    <logger name="org.springframework.context.support" level="WARN"/>
    <logger name="org.springframework.cloud.context" level="WARN"/>
    <logger name="org.springframework.boot.actuate" level="ERROR"/>
    <logger name="org.springframework.security.web" level="WARN"/>
    <logger name="org.springframework.cloud.stream" level="WARN"/>
    <logger name="org.springframework.integration" level="WARN"/>
    <logger name="org.springframework.scheduling" level="WARN"/>
    <logger name="de.codecentric.boot.admin" level="ERROR"/>
    <logger name="org.elasticsearch.client" level="ERROR"/>
    <logger name="org.springframework.data" level="ERROR"/>
    <logger name="org.springframework.amqp" level="WARN"/>
    <logger name="com.zaxxer.hikari.pool" level="ERROR"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.mybatis.spring" level="ERROR"/>
    <logger name="org.mongodb.driver" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="WARN"/>
    <logger name="org.eclipse.milo" level="ERROR"/>
    <logger name="io.lettuce.core" level="WARN"/>
    <logger name="com.sun.jersey" level="WARN"/>
    <logger name="org.openscada" level="ERROR"/>
    <logger name="org.jinterop" level="ERROR"/>
    <logger name="io.undertow" level="ERROR"/>
    <logger name="com.netflix" level="ERROR"/>
    <logger name="org.quartz" level="WARN"/>
    <logger name="org.jboss" level="ERROR"/>
    <logger name="org.xnio" level="WARN"/>

    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

</configuration>
