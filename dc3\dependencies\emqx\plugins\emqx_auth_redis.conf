#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## Redis Auth/ACL Plugin
##--------------------------------------------------------------------
## Redis Server cluster type
## single    Single redis server
## sentinel  Redis cluster through sentinel
## cluster   Redis through cluster
auth.redis.type = single

## Redis server address.
##
## Value: Port | IP:Port
##
## Single Redis Server: 127.0.0.1:6379, localhost:6379
## Redis Sentinel: 127.0.0.1:26379,*********:26379,*********:26379
## Redis Cluster: 127.0.0.1:6379,*********:6379,*********:6379
auth.redis.server = 127.0.0.1:6379

## Redis sentinel cluster name.
##
## Value: String
## auth.redis.sentinel = mymaster

## Redis pool size.
##
## Value: Number
auth.redis.pool = 8

## Redis database no.
##
## Value: Number
auth.redis.database = 0

## Redis password.
##
## Value: String
## auth.redis.password =

## Redis query timeout
##
## Value: Duration
## auth.redis.query_timeout = 5s

## Authentication query command.
##
## Value: Redis cmd
##
## Variables:
##  - %u: username
##  - %c: clientid
##  - %C: common name of client TLS cert
##  - %d: subject of client TLS cert
##
## Examples:
##  - HGET mqtt_user:%u password
##  - HMGET mqtt_user:%u password
##  - HMGET mqtt_user:%u password salt
auth.redis.auth_cmd = HMGET mqtt_user:%u password

## Password hash.
##
## Value: plain | md5 | sha | sha256 | bcrypt
auth.redis.password_hash = plain

## sha256 with salt prefix
## auth.redis.password_hash = salt,sha256

## sha256 with salt suffix
## auth.redis.password_hash = sha256,salt

## bcrypt with salt prefix
## auth.redis.password_hash = salt,bcrypt

## pbkdf2 with macfun iterations dklen
## macfun: md4, md5, ripemd160, sha, sha224, sha256, sha384, sha512
## auth.redis.password_hash = pbkdf2,sha256,1000,20

## Superuser query command.
##
## Value: Redis cmd
##
## Variables:
##  - %u: username
##  - %c: clientid
##  - %C: common name of client TLS cert
##  - %d: subject of client TLS cert
auth.redis.super_cmd = HGET mqtt_user:%u is_superuser

## ACL query command.
##
## Value: Redis cmd
##
## Variables:
##  - %u: username
##  - %c: clientid
auth.redis.acl_cmd = HGETALL mqtt_acl:%u

