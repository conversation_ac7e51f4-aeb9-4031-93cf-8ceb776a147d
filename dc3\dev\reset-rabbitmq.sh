#!/bin/bash

#
# Copyright 2022 Pnoker All Rights Reserved
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

set -e

cd ../

# Remove stopped rabbitmq container and volume
docker container stop dc3-rabbitmq
docker container rm dc3-rabbitmq
docker volume rm dc3_rabbitmq

# Rebuild rabbitmq and start
docker-compose build rabbitmq
docker-compose up -d rabbitmq