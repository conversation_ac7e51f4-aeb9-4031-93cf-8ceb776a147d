#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## EMQ X Dashboard
##--------------------------------------------------------------------

## Default user's login name.
##
## Value: String
dashboard.default_user.login = admin

## Default user's password.
##
## Value: String
dashboard.default_user.password = public

##--------------------------------------------------------------------
## HTTP Listener

## The port that the Dashboard HTTP listener will bind.
##
## Value: Port
##
## Examples: 18083
dashboard.listener.http = 3150

## The acceptor pool for external Dashboard HTTP listener.
##
## Value: Number
dashboard.listener.http.acceptors = 4

## Maximum number of concurrent Dashboard HTTP connections.
##
## Value: Number
dashboard.listener.http.max_clients = 512

## Set up the socket for IPv6.
##
## Value: false | true
dashboard.listener.http.inet6 = false

## Listen on IPv4 and IPv6 (false) or only on IPv6 (true). Use with inet6.
##
## Value: false | true
dashboard.listener.http.ipv6_v6only = false

##--------------------------------------------------------------------
## HTTPS Listener

## The port that the Dashboard HTTPS listener will bind.
##
## Value: Port
##
## Examples: 18084
## dashboard.listener.https = 18084

## The acceptor pool for external Dashboard HTTPS listener.
##
## Value: Number
## dashboard.listener.https.acceptors = 2

## Maximum number of concurrent Dashboard HTTPS connections.
##
## Value: Number
## dashboard.listener.https.max_clients = 512

## Set up the socket for IPv6.
##
## Value: false | true
## dashboard.listener.https.inet6 = false

## Listen on IPv4 and IPv6 (false) or only on IPv6 (true). Use with inet6.
##
## Value: false | true
## dashboard.listener.https.ipv6_v6only = false

## Path to the file containing the user's private PEM-encoded key.
##
## Value: File
## dashboard.listener.https.keyfile = etc/certs/key.pem

## Path to a file containing the user certificate.
##
## Value: File
## dashboard.listener.https.certfile = etc/certs/cert.pem

## Path to the file containing PEM-encoded CA certificates.
##
## Value: File
## dashboard.listener.https.cacertfile = etc/certs/cacert.pem

## See: 'listener.ssl.<name>.dhfile' in emq.conf
##
## Value: File
## dashboard.listener.https.dhfile = etc/certs/dh-params.pem

## See: 'listener.ssl.<name>.verify' in emq.conf
##
## Value: verify_peer | verify_none
## dashboard.listener.https.verify = verify_peer

## See: 'listener.ssl.<name>.fail_if_no_peer_cert' in emq.conf
##
## Value: false | true
## dashboard.listener.https.fail_if_no_peer_cert = true

## TLS versions only to protect from POODLE attack.
##
## Value: String, seperated by ','
## NOTE: Do not use tlsv1.3 if emqx is running on OTP-22 or earlier
## dashboard.listener.https.tls_versions = tlsv1.3,tlsv1.2,tlsv1.1,tlsv1

## See: 'listener.ssl.<name>.ciphers' in emq.conf
##
## Value: Ciphers
## dashboard.listener.https.ciphers = TLS_AES_256_GCM_SHA384,TLS_AES_128_GCM_SHA256,TLS_CHACHA20_POLY1305_SHA256,TLS_AES_128_CCM_SHA256,TLS_AES_128_CCM_8_SHA256,ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-ECDSA-AES256-SHA384,ECDHE-RSA-AES256-SHA384,ECDHE-ECDSA-DES-CBC3-SHA,ECDH-ECDSA-AES256-GCM-SHA384,ECDH-RSA-AES256-GCM-SHA384,ECDH-ECDSA-AES256-SHA384,ECDH-RSA-AES256-SHA384,DHE-DSS-AES256-GCM-SHA384,DHE-DSS-AES256-SHA256,AES256-GCM-SHA384,AES256-SHA256,ECDHE-ECDSA-AES128-GCM-SHA256,ECDHE-RSA-AES128-GCM-SHA256,ECDHE-ECDSA-AES128-SHA256,ECDHE-RSA-AES128-SHA256,ECDH-ECDSA-AES128-GCM-SHA256,ECDH-RSA-AES128-GCM-SHA256,ECDH-ECDSA-AES128-SHA256,ECDH-RSA-AES128-SHA256,DHE-DSS-AES128-GCM-SHA256,DHE-DSS-AES128-SHA256,AES128-GCM-SHA256,AES128-SHA256,ECDHE-ECDSA-AES256-SHA,ECDHE-RSA-AES256-SHA,DHE-DSS-AES256-SHA,ECDH-ECDSA-AES256-SHA,ECDH-RSA-AES256-SHA,AES256-SHA,ECDHE-ECDSA-AES128-SHA,ECDHE-RSA-AES128-SHA,DHE-DSS-AES128-SHA,ECDH-ECDSA-AES128-SHA,ECDH-RSA-AES128-SHA,AES128-SHA

## See: 'listener.ssl.<name>.secure_renegotiate' in emq.conf
##
## Value: on | off
## dashboard.listener.https.secure_renegotiate = off

## See: 'listener.ssl.<name>.reuse_sessions' in emq.conf
##
## Value: on | off
## dashboard.listener.https.reuse_sessions = on

## See: 'listener.ssl.<name>.honor_cipher_order' in emq.conf
##
## Value: on | off
## dashboard.listener.https.honor_cipher_order = on

