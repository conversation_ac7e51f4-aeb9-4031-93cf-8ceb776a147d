#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-center-monitor
  namespace: dc3
  labels:
    dc3.service: dc3-center-monitor
spec:
  ports:
    - name: "8200"
      port: 8200
      targetPort: 8200
  selector:
    dc3.service: dc3-center-monitor

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-center-monitor
  namespace: dc3
  labels:
    dc3.service: dc3-center-monitor
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-center-monitor
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-center-monitor
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-center-monitor
      volumes:
        - name: dc3-storage
          persistentVolumeClaim:
            claimName: dc3-persistent-volume-claim
      containers:
        - name: dc3-center-monitor
          image: pnoker/dc3-center-monitor:1.0.0
          ports:
            - containerPort: 8200
          env:
            - name: NODE_ENV
              value: "test"
            - name: MONITOR_CONTEXT_PATH
              value: "/dc3/monitor"
            - name: MONITOR_PUBLIC_URL
              value: "/dc3/monitor"
          resources:
            requests:
              cpu: "100m"
              memory: "200Mi"
            limits:
              cpu: "1000m"
              memory: "2000Mi"
          volumeMounts:
            - mountPath: "/dc3-center/dc3-center-monitor/dc3/logs"
              name: dc3-storage