#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

### --------------------- Point ---------------------
### 新增 Point
#/manager/point/add
POST http://localhost:8400/manager/point/add
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "name": "测试位号",
  "type": "string",
  "rw": 0,
  "base": 0,
  "minimum": -999999,
  "maximum": 999999,
  "multiple": 1,
  "accrue": 0,
  "format": "%.3f",
  "unit": "mm",
  "profileId": -1,
  "description": "测试位号"
}


### 修改 Point
#/manager/point/update
POST http://localhost:8400/manager/point/update
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "id": 1,
  "name": "测试位号",
  "type": "string",
  "rw": 0,
  "base": 0,
  "minimum": -999999,
  "maximum": 999999,
  "multiple": 1,
  "accrue": 0,
  "format": "%.3f",
  "unit": "mm",
  "profileId": -1,
  "description": "测试位号"
}


### 根据 ID 查询 Point
#/manager/point/id/:id
GET http://localhost:8400/manager/point/id/1
Accept: */*
Cache-Control: no-cache



### 分页查询 Point
#/manager/point/list，支持name模糊查询，type\rw\accrue\profileId精准查询
POST http://localhost:8400/manager/point/list
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "name": "",
  "page": {
    "current": 1,
    "orders": [
      {
        "column": "id",
        "asc": false
      }
    ]
  }
}


### 根据 ID 删除 Point
#/manager/point/delete/:id
POST http://localhost:8400/manager/point/delete/1
Accept: */*
Content-Type: application/json
Cache-Control: no-cache


### --------------------- Point Attribute ---------------------
### 新增 PointAttribute
#/manager/point_attribute/add
POST http://localhost:8400/manager/point_attribute/add
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "displayName": "测试IP",
  "name": "ip",
  "type": "string",
  "value": "localhost",
  "driverId": -1,
  "description": "测试位号属性"
}


### 修改 PointAttribute
#/manager/point_attribute/update
POST http://localhost:8400/manager/point_attribute/update
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "id": 1,
  "displayName": "测试IP",
  "name": "ip",
  "type": "string",
  "value": "localhost",
  "driverId": -1,
  "description": "测试位号属性"
}


### 根据 ID 查询 PointAttribute
#/manager/point_attribute/id/:id
GET http://localhost:8400/manager/point_attribute/id/1
Accept: */*
Cache-Control: no-cache



### 分页查询 PointAttribute
#/manager/point_attribute/list，支持displayName模糊查询，name\type\driverId精准查询
POST http://localhost:8400/manager/point_attribute/list
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "displayName": "",
  "page": {
    "current": 1,
    "orders": [
      {
        "column": "id",
        "asc": false
      }
    ]
  }
}


### 根据 ID 删除 PointAttribute
#/manager/point_attribute/delete/:id
POST http://localhost:8400/manager/point_attribute/delete/1
Accept: */*
Content-Type: application/json
Cache-Control: no-cache


### --------------------- Point Info ---------------------
### 新增 PointInfo
#/manager/point_info/add
POST http://localhost:8400/manager/point_info/add
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "pointAttributeId": 1,
  "value": "localhost",
  "deviceId": -1,
  "pointId": -1,
  "description": "测试位号配置"
}


### 修改 PointInfo
#/manager/point_info/update
POST http://localhost:8400/manager/point_info/update
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "id": 1,
  "pointAttributeId": 1,
  "value": "localhost",
  "deviceId": -1,
  "pointId": -1,
  "description": "测试位号配置"
}


### 根据 ID 查询 PointInfo
#/manager/point_info/id/:id
GET http://localhost:8400/manager/point_info/id/1
Accept: */*
Cache-Control: no-cache



### 分页查询 PointInfo
#/manager/point_info/list，支持pointAttributeId\deviceId\pointId精准查询
POST http://localhost:8400/manager/point_info/list
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "page": {
    "current": 1,
    "orders": [
      {
        "column": "id",
        "asc": false
      }
    ]
  }
}


### 根据 ID 删除 PointInfo
#/manager/point_info/delete/:id
POST http://localhost:8400/manager/point_info/delete/1
Accept: */*
Content-Type: application/json
Cache-Control: no-cache