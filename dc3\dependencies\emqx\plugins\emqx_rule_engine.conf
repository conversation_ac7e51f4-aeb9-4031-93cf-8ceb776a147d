#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##====================================================================
## Rule Engine for EMQ X R4.0
##====================================================================

rule_engine.ignore_sys_message = on

## Event Messages
##
## If enabled (on), rule engine publishes the event as an MQTT message
## with topic='$events/<event-name>' on the occurrence of an emqx event.
##
## If disabled, rule engine stops publishing the event messages, but
## the event message can still be processed by the rule SQL. e.g. rule SQL:
##
##   SELECT * FROM "$events/client_connected"
##
## will still work even if 'rule_engine.events.client_connected' is set to 'off'
##
## EMQ Event to event message mapping:
##
##   - client.connected      -> $events/client_connected
##   - client.disconnected   -> $events/client_disconnected
##   - session.subscribed    -> $events/session_subscribed
##   - session.unsubscribed  -> $events/session_unsubscribed
##   - message.delivered     -> $events/message_delivered
##   - message.acked         -> $events/message_acked
##   - message.dropped       -> $events/message_dropped
##
## Config Value Format: Toggle, QoS-Level
##
## Toggle: on/off
##
## QoS-Level: qos0/qos1/qos2

#rule_engine.events.client_connected = on, qos1
rule_engine.events.client_connected = off
rule_engine.events.client_disconnected = off
rule_engine.events.session_subscribed = off
rule_engine.events.session_unsubscribed = off
rule_engine.events.message_delivered = off
rule_engine.events.message_acked = off
rule_engine.events.message_dropped = off
