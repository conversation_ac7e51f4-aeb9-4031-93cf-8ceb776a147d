#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

---
apiVersion: v1
kind: Namespace
metadata:
  name: dc3
  labels:
    dc3.version: 2022.1.0

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: dc3net
  namespace: dc3
spec:
  podSelector:
    matchLabels:
      dc3.network/dc3net: "true"
  ingress:
    - from:
        - podSelector:
            matchLabels:
              dc3.network/dc3net: "true"
    - ports:
        - protocol: TCP
          port: 80
        - protocol: TCP
          port: 8000
        - protocol: TCP
          port: 8100
        - protocol: TCP
          port: 8200
        - protocol: TCP
          port: 8300
        - protocol: TCP
          port: 8400
        - protocol: TCP
          port: 8500
        - protocol: TCP
          port: 3306
        - protocol: TCP
          port: 6379
        - protocol: TCP
          port: 1883
        - protocol: TCP
          port: 5672
        - protocol: TCP
          port: 15672
        - protocol: TCP
          port: 27017
        - protocol: TCP
          port: 61613
  egress:
    - { }

---
apiVersion: v1
kind: Secret
metadata:
  labels:
    k8s-app: dc3
  name: dc3-secret
  namespace: dc3
type: Opaque

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dc3-ingress
  namespace: dc3
  annotations:
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
    - http:
        paths:
          - path: /dc3/demo
            pathType: Prefix
            backend:
              service:
                name: dc3-web
                port:
                  number: 80
          - path: /dc3/gateway
            pathType: Prefix
            backend:
              service:
                name: dc3-center-monitor
                port:
                  number: 8000
          - path: /dc3/register
            pathType: Prefix
            backend:
              service:
                name: dc3-center-register
                port:
                  number: 8100
          - path: /dc3/monitor
            pathType: Prefix
            backend:
              service:
                name: dc3-center-monitor
                port:
                  number: 8200
          - path: /eureka/
            pathType: Prefix
            backend:
              service:
                name: dc3-center-register
                port:
                  number: 8100
          - path: /monitor/
            pathType: Prefix
            backend:
              service:
                name: dc3-center-monitor
                port:
                  number: 8200

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-web
  namespace: dc3
  labels:
    dc3.service: dc3-web
spec:
  ports:
    - name: "80"
      port: 80
      targetPort: 80
    - name: "443"
      port: 443
      targetPort: 443
  selector:
    dc3.service: dc3-web

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-center-register
  namespace: dc3
  labels:
    dc3.service: dc3-center-register
spec:
  ports:
    - name: "8100"
      port: 8100
      targetPort: 8100
  selector:
    dc3.service: dc3-center-register

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-center-monitor
  namespace: dc3
  labels:
    dc3.service: dc3-center-monitor
spec:
  ports:
    - name: "8200"
      port: 8200
      targetPort: 8200
  selector:
    dc3.service: dc3-center-monitor

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-gateway
  namespace: dc3
  labels:
    dc3.service: dc3-gateway
spec:
  ports:
    - name: "8000"
      port: 8000
      targetPort: 8000
  selector:
    dc3.service: dc3-gateway

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-center-auth
  namespace: dc3
  labels:
    dc3.service: dc3-center-auth
spec:
  ports:
    - name: "8300"
      port: 8300
      targetPort: 8300
  selector:
    dc3.service: dc3-center-auth

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-center-manager
  namespace: dc3
  labels:
    dc3.service: dc3-center-manager
spec:
  ports:
    - name: "8400"
      port: 8400
      targetPort: 8400
  selector:
    dc3.service: dc3-center-manager

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-center-data
  namespace: dc3
  labels:
    dc3.service: dc3-center-data
spec:
  ports:
    - name: "8500"
      port: 8500
      targetPort: 8500
  selector:
    dc3.service: dc3-center-data

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-mysql
  namespace: dc3
  labels:
    dc3.service: dc3-mysql
spec:
  ports:
    - name: "3306"
      port: 3306
      targetPort: 3306
  selector:
    dc3.service: dc3-mysql

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-redis
  namespace: dc3
  labels:
    dc3.service: dc3-redis
spec:
  ports:
    - name: "6379"
      port: 6379
      targetPort: 6379
  selector:
    dc3.service: dc3-redis

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-rabbitmq
  namespace: dc3
  labels:
    dc3.service: dc3-rabbitmq
spec:
  ports:
    - name: "5672"
      port: 5672
      targetPort: 5672
    - name: "1883"
      port: 1883
      targetPort: 1883
    - name: "61613"
      port: 61613
      targetPort: 61613
    - name: "15672"
      port: 15672
      targetPort: 15672
  selector:
    dc3.service: dc3-rabbitmq

---
apiVersion: v1
kind: Service
metadata:
  name: dc3-mongo
  namespace: dc3
  labels:
    dc3.service: dc3-mongo
spec:
  ports:
    - name: "27017"
      port: 27017
      targetPort: 27017
  selector:
    dc3.service: dc3-mongo

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-web
  namespace: dc3
  labels:
    dc3.service: dc3-web
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-web
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-web
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-web
      containers:
        - name: dc3-web
          image: dc3.local/dc3-web:1.0.1
          ports:
            - containerPort: 80
            - containerPort: 443
          env:
            - name: NODE_ENV
              value: "test"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-center-register
  namespace: dc3
  labels:
    dc3.service: dc3-center-register
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-center-register
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-center-register
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-center-register
      containers:
        - name: dc3-center-register
          image: dc3.local/dc3-center-register:1.0.0
          ports:
            - containerPort: 8100
          env:
            - name: NODE_ENV
              value: "test"
            - name: EUREKA_DASHBOARD_PATH
              value: "/dc3/register"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-center-monitor
  namespace: dc3
  labels:
    dc3.service: dc3-center-monitor
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-center-monitor
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-center-monitor
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-center-monitor
      containers:
        - name: dc3-center-monitor
          image: dc3.local/dc3-center-monitor:1.0.0
          ports:
            - containerPort: 8200
          env:
            - name: NODE_ENV
              value: "test"
            - name: MONITOR_CONTEXT_PATH
              value: "/dc3/monitor"
            - name: MONITOR_PUBLIC_URL
              value: "/dc3/monitor"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-gateway
  namespace: dc3
  labels:
    dc3.service: dc3-gateway
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-gateway
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-gateway
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-gateway
      containers:
        - name: dc3-gateway
          image: dc3.local/dc3-gateway:1.0.0
          ports:
            - containerPort: 8000
          env:
            - name: NODE_ENV
              value: "test"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-center-auth
  namespace: dc3
  labels:
    dc3.service: dc3-center-auth
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-center-auth
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-center-auth
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-center-auth
      containers:
        - name: dc3-center-auth
          image: dc3.local/dc3-center-auth:1.0.0
          ports:
            - containerPort: 8300
          env:
            - name: NODE_ENV
              value: "test"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-center-manager
  namespace: dc3
  labels:
    dc3.service: dc3-center-manager
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-center-manager
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-center-manager
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-center-manager
      containers:
        - name: dc3-center-manager
          image: dc3.local/dc3-center-manager:1.0.0
          ports:
            - containerPort: 8400
          env:
            - name: NODE_ENV
              value: "test"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-center-data
  namespace: dc3
  labels:
    dc3.service: dc3-center-data
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-center-data
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-center-data
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-center-data
      containers:
        - name: dc3-center-data
          image: dc3.local/dc3-center-data:1.0.0
          ports:
            - containerPort: 8500
          env:
            - name: NODE_ENV
              value: "test"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-mysql
  namespace: dc3
  labels:
    dc3.service: dc3-mysql
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-mysql
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-mysql
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-mysql
      containers:
        - name: dc3-mysql
          image: dc3.local/dc3-mysql:1.0.0
          ports:
            - containerPort: 3306

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-redis
  namespace: dc3
  labels:
    dc3.service: dc3-redis
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-redis
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-redis
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-redis
      containers:
        - name: dc3-redis
          image: dc3.local/dc3-redis:1.0.0
          ports:
            - containerPort: 6379

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-rabbitmq
  namespace: dc3
  labels:
    dc3.service: dc3-rabbitmq
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-rabbitmq
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-rabbitmq
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-rabbitmq
      containers:
        - name: dc3-rabbitmq
          image: dc3.local/dc3-rabbitmq:1.0.0
          ports:
            - containerPort: 5672
            - containerPort: 1883
            - containerPort: 61613
            - containerPort: 15672

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dc3-mongo
  namespace: dc3
  labels:
    dc3.service: dc3-mongo
spec:
  replicas: 1
  selector:
    matchLabels:
      dc3.service: dc3-mongo
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        dc3.service: dc3-mongo
        dc3.network/dc3net: "true"
    spec:
      hostname: dc3-mongo
      containers:
        - name: dc3-mongo
          image: dc3.local/dc3-mongo:1.0.0
          ports:
            - containerPort: 27017
