rtmp:
  ffmpeg:
    window: D:/Program Files/FFmpeg/bin/ffmpeg.exe
    unix: /usr/local/bin/ffmpeg

server:
  port: 8803

spring:
  application:
    name: @project.artifactId@
  profiles:
    active:
      - server
      - register
      - monitor
      - mysql
      - redis
      - feign
      - ${NODE_ENV:dev}
  main:
    allow-bean-definition-overriding: true
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  redis:
    database: 2
  cache:
    redis:
      time-to-live: ${CACHE_REDIS_TIME_TO_LIVE:12H}

logging:
  level:
    io.github.pnoker: DEBUG
  file:
    name: dc3/logs/transfer/rtmp/${spring.application.name}.log