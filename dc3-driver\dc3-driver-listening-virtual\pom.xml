<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2022 Pnoker All Rights Reserved
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.github.pnoker</groupId>
        <artifactId>dc3-driver</artifactId>
        <version>2022.1.0</version>
    </parent>

    <artifactId>dc3-driver-listening-virtual</artifactId>
    <packaging>jar</packaging>

    <description>IOT DC3 平台 ListeningVirtual驱动，监听模式，提供 Tcp 和 Udp 两种模式，仅用于测试用途。</description>

    <dependencies>

        <!--Netty-->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>

    </dependencies>

</project>