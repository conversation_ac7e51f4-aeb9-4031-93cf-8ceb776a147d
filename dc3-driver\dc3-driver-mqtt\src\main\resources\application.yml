#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

driver:
  name: MqttDriver
  type: driver
  project: @project.artifactId@
  description: @project.description@
  schedule:
    status:
      enable: true
      corn: '0/10 * * * * ?'
    read:
      enable: false
      corn: '0/30 * * * * ?'
    custom:
      enable: true
      corn:  '0/5 * * * * ?'
  point-attribute:
    - displayName: 指令Topic
      name: commandTopic
      type: string
      value: commandTopic
      description: 测点/设备接收下行指令的Mqtt主题
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
    - displayName: 指令Qos
      name: commandQos
      type: int
      value: 2
      description: 测点/设备接收下行指令的Mqtt主题的Qos
      option:
        type: input\select\checkox\switch\time...
        required: true
        data-type: static/url
        data: jsonString
  mqtt:
    url: ssl://dc3-emqx:8883
    auth-type: X509
    username: dc3
    password: dc3
    ca-crt: classpath:/certs/ca.crt
    client-key-pass: dc3-client
    client-key: classpath:/certs/client.key
    client-crt: classpath:/certs/client.crt
    client:  ${spring.application.name}
    receive-topics:
      - qos: 1
        name: driver/${spring.application.name}/device/+
      - qos: 1
        name: driver/${spring.application.name}/gateway/+
    default-send-topic:
      qos: 1
      name: default/${spring.application.name}
    keep-alive: 15
    completion-timeout: 3000
    batch:
      speed: ${MQTT_BATCH_SPEED:100}
      interval: ${MQTT_BATCH_INTERVAL:5}

server:
  port: 8701

spring:
  profiles:
    active:
      - server
      - register
      - monitor
      - rabbitmq
      - quartz
      - driver
      - feign
      - ${NODE_ENV:dev}

logging:
  file:
    name: dc3/logs/driver/mqtt/${spring.application.name}.log