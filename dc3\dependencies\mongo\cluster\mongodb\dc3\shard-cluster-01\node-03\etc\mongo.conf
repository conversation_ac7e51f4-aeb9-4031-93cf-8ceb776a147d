processManagement:
    fork: true
    pidFilePath: /data/mongodb/dc3/shard-cluster-01/node-03/mongos.pid

systemLog:
    destination: file
    # 指定 mongod 服务日志文件目录，其他节点以此类推
    path: /data/mongodb/dc3/shard-cluster-01/node-03/logs/mongodb.log
    logAppend: true

storage:
    journal:
        enabled: true
    # 指定数存放的路径，其他节点以此类推
    dbPath: /data/mongodb/dc3/shard-cluster-01/node-03/data/
    directoryPerDB: true
    # 选择存储引擎
    engine: wiredTiger
    wiredTiger:
        engineConfig:
            # 指定存储引擎的 cache 大小
            cacheSizeGB: 20
            directoryForIndexes: true
        collectionConfig:
            blockCompressor: snappy
        indexConfig:
            prefixCompression: true

net:
    # 设置 mongod 监听端口
    port: 27119
    # 设置最大连接数
    maxIncomingConnections: 10000
    bindIpAll: true

operationProfiling:
    # 设置慢日志时间
    slowOpThresholdMs: 100
    mode: slowOp

# 是否支持分片
sharding:
    clusterRole: shardsvr
    archiveMovedChunks: true

replication:
    oplogSizeMB: 10240
    # 表示这是 dc3_replica 集群的第一个分片
    # 该复制集中的所有 node 节点这个名字要一样
    # 如果是第二个复制集，这里可以取名 dc3_replica_2
    replSetName: dc3_replica_1

#security:
#    # 指定 keyfile 位置，其他节点以此类推
#    keyFile: /data/mongodb/dc3/shard-cluster-01/node-03/keys/keyfile
#    clusterAuthMode: keyFile
#    authorization: enabled