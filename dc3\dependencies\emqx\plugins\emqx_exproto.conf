#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##====================================================================
## EMQ X ExProto
##====================================================================

##--------------------------------------------------------------------
## Listeners
##--------------------------------------------------------------------

##--------------------------------------------------------------------
## MQTT/TCP - External TCP Listener for MQTT Protocol

## The IP address and port that the listener will bind.
##
## Value: <tcp|ssl|udp|dtls>://<ip>:<port>
##
## Examples: tcp://0.0.0.0:7993 | ssl://127.0.0.1:7994
exproto.listener.protoname = tcp://0.0.0.0:7993

## Driver type
##
## Value: python3 | java
exproto.listener.protoname.driver = python3

## The Search path for driver codes
##
exproto.listener.protoname.driver_search_path = data/extension

## The driver callback module/class name
##
#exproto.listener.protoname.driver_callback_module = main

## The acceptor pool for external MQTT/TCP listener.
##
## Value: Number
exproto.listener.protoname.acceptors = 8

## Maximum number of concurrent MQTT/TCP connections.
##
## Value: Number
exproto.listener.protoname.max_connections = 1024000

## Maximum external connections per second.
##
## Value: Number
exproto.listener.protoname.max_conn_rate = 1000

## Specify the {active, N} option for the external MQTT/TCP Socket.
##
## Value: Number
exproto.listener.protoname.active_n = 100

## Idle timeout
##
## Value: Duration
exproto.listener.protoname.idle_timeout = 30s

## The access control rules for the MQTT/TCP listener.
##
## See: https://github.com/emqtt/esockd#allowdeny
##
## Value: ACL Rule
##
## Example: allow 192.168.0.0/24
exproto.listener.protoname.access.1 = allow all

## Enable the Proxy Protocol V1/2 if the EMQ X cluster is deployed
## behind HAProxy or Nginx.
##
## See: https://www.haproxy.com/blog/haproxy/proxy-protocol/
##
## Value: on | off
## exproto.listener.protoname.proxy_protocol = on

## Sets the timeout for proxy protocol. EMQ X will close the TCP connection
## if no proxy protocol packet recevied within the timeout.
##
## Value: Duration
#exproto.listener.protoname.proxy_protocol_timeout = 3s

## The TCP backlog defines the maximum length that the queue of pending
## connections can grow to.
##
## Value: Number >= 0
exproto.listener.protoname.backlog = 1024

## The TCP send timeout for external MQTT connections.
##
## Value: Duration
exproto.listener.protoname.send_timeout = 15s

## Close the TCP connection if send timeout.
##
## Value: on | off
exproto.listener.protoname.send_timeout_close = on

## The TCP receive buffer(os kernel) for MQTT connections.
##
## See: http://erlang.org/doc/man/inet.html
##
## Value: Bytes
#exproto.listener.protoname.recbuf = 2KB

## The TCP send buffer(os kernel) for MQTT connections.
##
## See: http://erlang.org/doc/man/inet.html
##
## Value: Bytes
#exproto.listener.protoname.sndbuf = 2KB

## The size of the user-level software buffer used by the driver.
## Not to be confused with options sndbuf and recbuf, which correspond
## to the Kernel socket buffers. It is recommended to have val(buffer)
## >= max(val(sndbuf),val(recbuf)) to avoid performance issues because
## of unnecessary copying. val(buffer) is automatically set to the above
## maximum when values sndbuf or recbuf are set.
##
## See: http://erlang.org/doc/man/inet.html
##
## Value: Bytes
#exproto.listener.protoname.buffer = 2KB

## Sets the 'buffer = max(sndbuf, recbuf)' if this option is enabled.
##
## Value: on | off
#exproto.listener.protoname.tune_buffer = off

## The TCP_NODELAY flag for MQTT connections. Small amounts of data are
## sent immediately if the option is enabled.
##
## Value: true | false
exproto.listener.protoname.nodelay = true

## The SO_REUSEADDR flag for TCP listener.
##
## Value: true | false
exproto.listener.protoname.reuseaddr = true


##--------------------------------------------------------------------
## TLS/DTLS options

## TLS versions only to protect from POODLE attack.
##
## See: http://erlang.org/doc/man/ssl.html
##
## Value: String, seperated by ','
#exproto.listener.protoname.tls_versions = tlsv1.2,tlsv1.1,tlsv1

## Path to the file containing the user's private PEM-encoded key.
##
## See: http://erlang.org/doc/man/ssl.html
##
## Value: File
#exproto.listener.protoname.keyfile = etc/certs/key.pem

## Path to a file containing the user certificate.
##
## See: http://erlang.org/doc/man/ssl.html
##
## Value: File
#exproto.listener.protoname.certfile = etc/certs/cert.pem

## Path to the file containing PEM-encoded CA certificates. The CA certificates
## are used during server authentication and when building the client certificate chain.
##
## Value: File
#exproto.listener.protoname.cacertfile = etc/certs/cacert.pem

## The Ephemeral Diffie-Helman key exchange is a very effective way of
## ensuring Forward Secrecy by exchanging a set of keys that never hit
## the wire. Since the DH key is effectively signed by the private key,
## it needs to be at least as strong as the private key. In addition,
## the default DH groups that most of the OpenSSL installations have
## are only a handful (since they are distributed with the OpenSSL
## package that has been built for the operating system it’s running on)
## and hence predictable (not to mention, 1024 bits only).
## In order to escape this situation, first we need to generate a fresh,
## strong DH group, store it in a file and then use the option above,
## to force our SSL application to use the new DH group. Fortunately,
## OpenSSL provides us with a tool to do that. Simply run:
## openssl dhparam -out dh-params.pem 2048
##
## Value: File
#exproto.listener.protoname.dhfile = etc/certs/dh-params.pem

## A server only does x509-path validation in mode verify_peer,
## as it then sends a certificate request to the client (this
## message is not sent if the verify option is verify_none).
## You can then also want to specify option fail_if_no_peer_cert.
## More information at: http://erlang.org/doc/man/ssl.html
##
## Value: verify_peer | verify_none
#exproto.listener.protoname.verify = verify_peer

## Used together with {verify, verify_peer} by an SSL server. If set to true,
## the server fails if the client does not have a certificate to send, that is,
## sends an empty certificate.
##
## Value: true | false
#exproto.listener.protoname.fail_if_no_peer_cert = true

## This is the single most important configuration option of an Erlang SSL
## application. Ciphers (and their ordering) define the way the client and
## server encrypt information over the wire, from the initial Diffie-Helman
## key exchange, the session key encryption ## algorithm and the message
## digest algorithm. Selecting a good cipher suite is critical for the
## application’s data security, confidentiality and performance.
##
## The cipher list above offers:
##
## A good balance between compatibility with older browsers.
## It can get stricter for Machine-To-Machine scenarios.
## Perfect Forward Secrecy.
## No old/insecure encryption and HMAC algorithms
##
## Most of it was copied from Mozilla’s Server Side TLS article
##
## Value: Ciphers
#exproto.listener.protoname.ciphers = ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-ECDSA-AES256-SHA384,ECDHE-RSA-AES256-SHA384,ECDHE-ECDSA-DES-CBC3-SHA,ECDH-ECDSA-AES256-GCM-SHA384,ECDH-RSA-AES256-GCM-SHA384,ECDH-ECDSA-AES256-SHA384,ECDH-RSA-AES256-SHA384,DHE-DSS-AES256-GCM-SHA384,DHE-DSS-AES256-SHA256,AES256-GCM-SHA384,AES256-SHA256,ECDHE-ECDSA-AES128-GCM-SHA256,ECDHE-RSA-AES128-GCM-SHA256,ECDHE-ECDSA-AES128-SHA256,ECDHE-RSA-AES128-SHA256,ECDH-ECDSA-AES128-GCM-SHA256,ECDH-RSA-AES128-GCM-SHA256,ECDH-ECDSA-AES128-SHA256,ECDH-RSA-AES128-SHA256,DHE-DSS-AES128-GCM-SHA256,DHE-DSS-AES128-SHA256,AES128-GCM-SHA256,AES128-SHA256,ECDHE-ECDSA-AES256-SHA,ECDHE-RSA-AES256-SHA,DHE-DSS-AES256-SHA,ECDH-ECDSA-AES256-SHA,ECDH-RSA-AES256-SHA,AES256-SHA,ECDHE-ECDSA-AES128-SHA,ECDHE-RSA-AES128-SHA,DHE-DSS-AES128-SHA,ECDH-ECDSA-AES128-SHA,ECDH-RSA-AES128-SHA,AES128-SHA

## Ciphers for TLS PSK.
## Note that 'listener.ssl.external.ciphers' and 'listener.ssl.external.psk_ciphers' cannot
## be configured at the same time.
## See 'https://tools.ietf.org/html/rfc4279#section-2'.
#exproto.listener.protoname.psk_ciphers = PSK-AES128-CBC-SHA,PSK-AES256-CBC-SHA,PSK-3DES-EDE-CBC-SHA,PSK-RC4-SHA

## SSL parameter renegotiation is a feature that allows a client and a server
## to renegotiate the parameters of the SSL connection on the fly.
## RFC 5746 defines a more secure way of doing this. By enabling secure renegotiation,
## you drop support for the insecure renegotiation, prone to MitM attacks.
##
## Value: on | off
#exproto.listener.protoname.secure_renegotiate = off

## A performance optimization setting, it allows clients to reuse
## pre-existing sessions, instead of initializing new ones.
## Read more about it here.
##
## See: http://erlang.org/doc/man/ssl.html
##
## Value: on | off
#exproto.listener.protoname.reuse_sessions = on

## An important security setting, it forces the cipher to be set based
## on the server-specified order instead of the client-specified order,
## hence enforcing the (usually more properly configured) security
## ordering of the server administrator.
##
## Value: on | off
#exproto.listener.protoname.honor_cipher_order = on
