#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

### --------------------- Token ---------------------
### 生成用户 Salt
#/auth/token/salt
POST http://localhost:8300/auth/token/salt
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "name": "pnoker"
}


### 生成用户 Token 令牌
#/auth/token/generate
# pnoker/10e339be1130a90dc1b9ff0332abced6
# Dc3Util.md5("10e339be1130a90dc1b9ff0332abced6"+"verfdkkq")
POST http://localhost:8300/auth/token/generate
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "tenant": "default",
  "name": "pnoker",
  "salt": "dsb785i4ikx0h4wt",
  "password": "8f9812cee01121acb1f3cab6a08e58c4"
}


### 检测用户 Token 令牌是否有效
#/auth/token/:token
POST http://localhost:8300/auth/token/check
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "name": "pnoker",
  "salt": "dsb785i4ikx0h4wt",
  "token": "eyJhbGciOiJIUzI1NiJ9.********************************************************************************************.WeE8iLppqPhNNqhNFTlGvEiq6EQBhdxb7z8AER8N_vQ"
}


### 注销用户 Token 令牌
#/auth/token/cancel
POST http://localhost:8300/auth/token/cancel
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "name": "pnoker"
}