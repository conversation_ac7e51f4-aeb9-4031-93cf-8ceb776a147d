/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.github.pnoker.center.manager.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.github.pnoker.common.bean.Dictionary;
import io.github.pnoker.common.dto.DictionaryDto;

/**
 * Dictionary Interface
 *
 * <AUTHOR>
 */
public interface DictionaryService {

    /**
     * 获取驱动字典
     *
     * @param dictionaryDto DictionaryDto
     * @return Dictionary Page
     */
    Page<Dictionary> driverDictionary(DictionaryDto dictionaryDto);

    /**
     * 获取设备字典
     *
     * @param dictionaryDto DictionaryDto
     * @return Dictionary Page
     */
    Page<Dictionary> deviceDictionary(DictionaryDto dictionaryDto);

    /**
     * 获取模板字典
     *
     * @param dictionaryDto DictionaryDto
     * @return Dictionary Page
     */
    Page<Dictionary> profileDictionary(DictionaryDto dictionaryDto);

    /**
     * 获取位号字典
     * profile/device
     *
     * @param dictionaryDto DictionaryDto
     * @return Dictionary Page
     */
    Page<Dictionary> pointDictionary(DictionaryDto dictionaryDto);

}
