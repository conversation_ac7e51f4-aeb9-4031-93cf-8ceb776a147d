#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

### 新增 Group
#/manager/group/add
POST http://localhost:8400/manager/group/add
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "name": "测试分组",
  "description": "测试分组"
}


### 修改 Group
#/manager/group/update
POST http://localhost:8400/manager/group/update
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "id": 1,
  "name": "测试分组",
  "description": "测试分组"
}


### 根据 ID 查询 Group
#/manager/group/id/:id
GET http://localhost:8400/manager/group/id/-1
Accept: */*
Cache-Control: no-cache



### 分页查询 Group
#/manager/group/list，支持name模糊查询
POST http://localhost:8400/manager/group/list
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

{
  "name": "",
  "page": {
    "current": 1,
    "orders": [
      {
        "column": "id",
        "asc": false
      }
    ]
  }
}


### 根据 ID 删除 Group
#/manager/group/delete/:id
POST http://localhost:8400/manager/group/delete/1
Accept: */*
Content-Type: application/json
Cache-Control: no-cache