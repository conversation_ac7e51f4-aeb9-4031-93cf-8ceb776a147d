#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

server:
  port: 8200

spring:
  application:
    name: @project.artifactId@
  profiles:
    active:
      - server
      - register
      - monitor
      - ${NODE_ENV:dev}
  boot:
    admin:
      context-path: ${MONITOR_CONTEXT_PATH:/}
      ui:
        title: DC3 Monitor Center
        public-url: ${MONITOR_PUBLIC_URL:/}
        favicon: /monitor/images/icon/favicon.ico
        favicon-danger: /monitor/images/icon/favicon-danger.ico
        brand: <img src="/monitor/images/logo/dc3-logo.png"><span>DC3 Monitor Center</span>

logging:
  level:
    io.github.pnoker: DEBUG
  file:
    name: dc3/logs/center/monitor/${spring.application.name}.log