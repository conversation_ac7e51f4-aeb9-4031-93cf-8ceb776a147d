#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

server:
  port: 8400

spring:
  application:
    name: @project.artifactId@
  profiles:
    active:
      - server
      - register
      - monitor
      - rabbitmq
      - mysql
      - mongo
      - redis
      - feign
      - ${NODE_ENV:dev}
  main:
    allow-bean-definition-overriding: true
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  redis:
    database: 2
  cache:
    redis:
      time-to-live: ${CACHE_REDIS_TIME_TO_LIVE:12H}

logging:
  level:
    io.github.pnoker: DEBUG
    #io.github.pnoker.center.manager.service.rabbit: INFO
  file:
    name: dc3/logs/center/manager/${spring.application.name}.log
