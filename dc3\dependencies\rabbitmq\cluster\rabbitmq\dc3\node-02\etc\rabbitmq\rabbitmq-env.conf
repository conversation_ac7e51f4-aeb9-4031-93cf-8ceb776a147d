#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Example rabbitmq-env.conf file entries. Note that the variables
# do not have the RABBITMQ_ prefix.
#
# Overrides node name
#NODENAME=rabbit@$HOSTNAME
NODENAME=node-02@localhost

# Port used for inter-node and CLI tool communication.
# Ignored if node config file sets kernel.inet_dist_listen_min or kernel.inet_dist_listen_max keys.
# See Networking for details, and Windows Quirks for Windows-specific details.
RABBITMQ_DIST_PORT=25100

# overrides primary config file location
CONFIG_FILE=$RABBITMQ_HOME/etc/rabbitmq/rabbitmq.conf

# overrides advanced config file location
ADVANCED_CONFIG_FILE=$RABBITMQ_HOME/etc/rabbitmq/advanced.config

# overrides environment variable file location
CONF_ENV_FILE=$RABBITMQ_HOME/etc/rabbitmq/rabbitmq-env.conf