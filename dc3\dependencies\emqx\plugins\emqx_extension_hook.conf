#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##====================================================================
## EMQ X Hooks
##====================================================================

##--------------------------------------------------------------------
## Driver confs

## Setup the supported drivers
##
## Value: python3 | java
exhook.drivers = python3

## Search path for scripts/library
##
exhook.drivers.python3.path = data/extension/

## Call timeout
##
## Value: Duration
##exhook.drivers.python3.call_timeout = 5s

## Initial module name
##
##exhook.drivers.python3.init_module = main
