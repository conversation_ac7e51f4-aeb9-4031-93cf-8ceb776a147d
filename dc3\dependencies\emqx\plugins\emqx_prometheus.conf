#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## emqx_prometheus for EMQ X
##--------------------------------------------------------------------

## The Prometheus Push Gateway URL address
##
## Note: You can comment out this line to disable it
prometheus.push.gateway.server = http://127.0.0.1:9091

## The metrics data push interval (millisecond)
##
## Default: 15000
prometheus.interval = 15000
