#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

##--------------------------------------------------------------------
## EMQ X Retainer
##--------------------------------------------------------------------

## Where to store the retained messages.
##
## Notice that all nodes in the same cluster have to be configured to
## use the same storage_type.
##
## Value: ram | disc | disc_only
##  - ram: memory only
##  - disc: both memory and disc
##  - disc_only: disc only
##
## Default: ram
retainer.storage_type = ram

## Maximum number of retained messages. 0 means no limit.
##
## Value: Number >= 0
retainer.max_retained_messages = 0

## Maximum retained message size.
##
## Value: Bytes
retainer.max_payload_size = 1MB

## Expiry interval of the retained messages. Never expire if the value is 0.
##
## Value: Duration
##  - h: hour
##  - m: minute
##  - s: second
##
## Examples:
##  - 2h:  2 hours
##  - 30m: 30 minutes
##  - 20s: 20 seconds
##
## Defaut: 0
retainer.expiry_interval = 0
