#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: dc3-persistent-volume
  namespace: dc3
  labels:
    dc3.version: 2022.1.0
spec:
  storageClassName: manual
  persistentVolumeReclaimPolicy: Recycle
  accessModes:
    - ReadOnlyMany
  capacity:
    storage: 10Gi
  hostPath:
    path: "/mnt/data"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: dc3-persistent-volume-claim
  namespace: dc3
  labels:
    dc3.version: 2022.1.0
spec:
  storageClassName: manual
  accessModes:
    - ReadOnlyMany
  resources:
    requests:
      storage: 3Gi