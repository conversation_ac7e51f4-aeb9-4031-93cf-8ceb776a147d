# Development environment, customizable configuration
driver:
  schedule:
    read:
      corn: '0/5 * * * * ?'

eureka:
  instance:
    # Custom node registration IP
    ip-address: ${SERVICE_HOST:*************}

# Log config
logging:
  level:
    io.github.pnoker: DEBUG
    io.github.pnoker.common.sdk: DEBUG
    io.github.pnoker.common.sdk.service: DEBUG
    io.github.pnoker.common.sdk.service.job: DEBUG
    io.github.pnoker.common.sdk.service.rabbit: DEBUG