#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

language: java
jdk:
  - openjdk8
services:
  - docker
before_install:
  - sudo service mysql stop
branches:
  only:
    - master
install: mvn -s dc3/dependencies/maven/settings.xml package
script: mvn cobertura:cobertura
after_success:
  - bash <(curl -s https://codecov.io/bash)
