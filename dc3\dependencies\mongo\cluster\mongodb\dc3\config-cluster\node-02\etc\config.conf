processManagement:
    fork: true
    pidFilePath: /data/mongodb/dc3/config-cluster/node-02/mongos.pid

systemLog:
    destination: file
    # 指定 mongod 服务日志文件目录，其他节点以此类推
    path: /data/mongodb/dc3/config-cluster/node-02/logs/mongodb.log
    logAppend: true

storage:
    journal:
        enabled: true
    # 指定数存放的路径，其他节点以此类推
    dbPath: /data/mongodb/dc3/config-cluster/node-02/data/
    directoryPerDB: true
    # 选择存储引擎
    engine: wiredTiger
    wiredTiger:
        engineConfig:
            # 指定存储引擎的 cache 大小
            cacheSizeGB: 20
            directoryForIndexes: true
        collectionConfig:
            blockCompressor: snappy
        indexConfig:
            prefixCompression: true

net:
    # 设置 mongod 监听端口
    port: 27081
    # 设置最大连接数
    maxIncomingConnections: 10000
    bindIpAll: true

operationProfiling:
    # 设置慢日志时间
    slowOpThresholdMs: 100
    mode: slowOp

# 是否支持分片
sharding:
    clusterRole: configsvr
    archiveMovedChunks: true

replication:
    oplogSizeMB: 10240
    # 需要和 mongos configDB 配置中的名字一致
    replSetName: dc3_replica

#security:
#    # 指定 keyfile 位置，其他节点以此类推
#    keyFile: /data/mongodb/dc3/config-cluster/node-02/keys/keyfile
#    clusterAuthMode: keyFile
#    authorization: enabled