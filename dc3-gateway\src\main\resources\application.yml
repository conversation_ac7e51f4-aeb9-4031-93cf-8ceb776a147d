server:
  port: 8000

spring:
  application:
    name: @project.artifactId@
  profiles:
    active:
      - server
      - register
      - monitor
      - redis
      - feign
      - ${NODE_ENV:dev}
  main:
    allow-bean-definition-overriding: true
  thymeleaf:
    cache: false
    enabled: true
    check-template: true
    mode: HTML
    encoding: UTF-8
    prefix: classpath:/templates/
    suffix: .html
    servlet:
      content-type: text/html
  redis:
    database: 0
  cache:
    redis:
      time-to-live: ${CACHE_REDIS_TIME_TO_LIVE:12H}
  cloud:
    gateway:
      loadbalancer:
        use404: true
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # auth
        - id: auth_route
          uri: lb://dc3-center-auth
          predicates:
            - Path=/api/v3/auth/user/**,/api/v3/auth/black_ip/**,/api/v3/auth/tenant/**,/api/v3/auth/dictionary/**
          filters:
            - StripPrefix=2
            - Authentic
            - name: RequestRateLimiter
              args:
                key-resolver: '#{@hostKeyResolver}'
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
            - name: CircuitBreaker
              args:
                name: default
                fallbackUri: 'forward:/fallback'
        # manager
        - id: manager_route
          uri: lb://dc3-center-manager
          predicates:
            - Path=/api/v3/manager/**
          filters:
            - StripPrefix=2
            - Authentic
            - name: RequestRateLimiter
              args:
                key-resolver: '#{@hostKeyResolver}'
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
            - name: CircuitBreaker
              args:
                name: default
                fallbackUri: 'forward:/fallback'
        # data
        - id: data_route
          uri: lb://dc3-center-data
          predicates:
            - Path=/api/v3/data/**
          filters:
            - StripPrefix=2
            #- Authentic
            - name: RequestRateLimiter
              args:
                key-resolver: '#{@hostKeyResolver}'
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
            - name: CircuitBreaker
              args:
                name: default
                fallbackUri: 'forward:/fallback'
        # rtmp
        - id: rtmp_route
          uri: lb://dc3-transfer-rtmp
          predicates:
            - Path=/api/v3/rtmp/**
          filters:
            - StripPrefix=2
            - Authentic
            - name: RequestRateLimiter
              args:
                key-resolver: '#{@hostKeyResolver}'
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
            - name: CircuitBreaker
              args:
                name: default
                fallbackUri: 'forward:/fallback'

resilience4j:
  timelimiter:
    configs:
      default:
        timeout-duration: 15S

logging:
  level:
    io.github.pnoker: DEBUG
  file:
    name: dc3/logs/gateway/${spring.application.name}.log