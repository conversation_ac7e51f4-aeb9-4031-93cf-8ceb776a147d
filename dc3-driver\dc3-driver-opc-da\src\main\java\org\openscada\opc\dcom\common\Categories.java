/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.openscada.opc.dcom.common;

public interface Categories {
    public static final String OPCDAServer10 = "63D5F430-CFE4-11d1-B2C8-0060083BA1FB";

    public static final String OPCDAServer20 = "63D5F432-CFE4-11d1-B2C8-0060083BA1FB";

    public static final String OPCDAServer30 = "CC603642-66D7-48f1-B69A-B625E73652D7";

    public static final String XMLDAServer10 = "3098EDA4-A006-48b2-A27F-247453959408";

}
