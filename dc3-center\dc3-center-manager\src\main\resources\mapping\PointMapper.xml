<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2022 Pnoker All Rights Reserved
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.github.pnoker.center.manager.mapper.PointMapper">
    <select id="selectPageWithDevice" resultType="io.github.pnoker.common.model.Point">
        select
        dp.id,
        name,
        type,
        rw,
        base,
        minimum,
        maximum,
        multiple,
        accrue,
        format,
        unit,
        enable,
        dp.profile_id,
        group_id,
        tenant_id,
        dp.description,
        dp.create_time,
        dp.update_time,
        dp.deleted
        from dc3_point dp
        <if test="deviceId != null and deviceId != ''">
            inner join dc3_profile_bind dpb on dp.profile_id = dpb.profile_id and dpb.device_id = #{deviceId}
        </if>
        ${ew.customSqlSegment}
    </select>
</mapper>