#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

### 读取指定设备位号值
#driver/command/read
POST http://localhost:8603/driver/command/read
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

[
  {
    "deviceId": 1,
    "pointId": 1
  }
]


### 写入指定设备位号值
#driver/command/write
POST http://localhost:8603/driver/command/write
Accept: */*
Content-Type: application/json
Cache-Control: no-cache

[
  {
    "deviceId": 1,
    "pointId": 1,
    "value": "34"
  }
]

###