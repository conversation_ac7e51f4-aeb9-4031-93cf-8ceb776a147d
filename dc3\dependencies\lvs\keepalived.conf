#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Global Configuration
global_defs {
    lvs_id  node-01
}

# VRRP Configuration
vrrp_instance dc3_vip {
	state MASTER
	interface eno1
	priority 100
	advert_int 1

	#VRRP组名，多个节点的设置值必须一样，以指明各个节点属于同一个
	virtual_router_id 51
    #设置验证信息，同一个组的多个节点设置必须一致
    authentication {
        auth_type PASS
        auth_pass 1111
    }

	virtual_ipaddress {
		*************
	}
}

# Virtual Server Configuration
virtual_server ************* 5000 {
    delay_loop 1
    lb_algo rr
    lb_kind DR
    persistence_timeout 60
    protocol TCP

	# Real Server configuration
	real_server ************ 15672 {
		weight 3
		TCP_CHECK {
			delay_before_retry 3
			connect_port 5672 15672
		}
	}

	# Real Server configuration
	real_server 127.0.0.1 15673 {
		weight 3
		TCP_CHECK {
			delay_before_retry 3
			connect_port 5672 15672
		}
	}

}