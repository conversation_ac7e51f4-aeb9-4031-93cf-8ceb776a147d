## `EMQ X` 集群部署



> `EMQ X` 消息服务器集群基于 `Erlang/OTP` 分布式设计，集群原理可简述为下述两条规则:
>
> `MQTT` 客户端订阅主题时，所在节点订阅成功后广播通知其他节点：某个主题(`Topic`)被本节点订阅。
>
> `MQTT` 客户端发布消息时，所在节点会根据消息主题(`Topic`)，检索订阅并路由消息到相关节点。



### 1. 集群架构

![](../images/dc3/cluster/emqx/emqx-1.png)

> `EMQ X` 消息服务器同一集群的所有节点，都会复制一份主题(Topic) -> 节点(Node)映射的路由表，例如:
>
> ```bash
> topic1 -> node-01, node-02
> topic2 -> node-03
> topic3 -> node-02, node-04
> ```



### 2. 集群部署规划

|       `item`        |     `node 1`      |     `node 2`      |     `node 3`      |
| :-----------------: | :---------------: | :---------------: | :---------------: |
|     `node.name`     | node-01@127.0.0.1 | node-02@127.0.0.1 | node-03@127.0.0.1 |
| `mqtt:tcp:external` |       3000        |       3100        |       3200        |
| `mqtt:ssl:external` |       3010        |       3110        |       3210        |
| `mqtt:ws:external`  |       3020        |       3120        |       3220        |
| `mqtt:wss:external` |       3030        |       3130        |       3230        |
|  `http:management`  |       3040        |       3140        |       3240        |
|  `http:dashboard`   |       3050        |       3150        |       3250        |
| `mqtt:tcp:internal` |       13000       |       13100       |       13200       |



### 3. 安装文件

> **下载文件** 
>
> 请下载 `ubuntu zip` 版本

- 以 `4.3.10` 版本为例：https://www.emqx.com/zh/downloads/broker/4.3.10/emqx-ubuntu20.04-4.3.10-amd64.zip

- 其他版本下载：https://www.emqx.com/zh/try?product=broker



> **解压文件**

```bash
unzip emqx-ubuntu20.04-4.3.10-amd64.zip
```



### 4. 集群部署

#### 4.1 创建文件目录

>  分别创建三个节点（`node`）目录，多个以此类推 `node-N`
>

```bash
cd /data

mkdir -p emqx/dc3/node-01 emqx/dc3/node-02 emqx/dc3/node-03
```



> 将解压的 `emqx` 文件放入到每个节点中，其他节点操作一致

```bash
cp -r emqx* emqx/dc3/node-01/

# pwd
# /data/emqx/dc3/node-01

# ls
# bin data dynlibs erts-11.1.8 etc lib log releases
```



#### 4.2 配置文件

>  在每个节点的 `etc` 下配置文件 `emqx.conf `、`plugins/emqx_dashboard.conf `、`plugins/emqx_management.conf `



##### 4.2.1 `emqx.conf `

```yaml
## EMQ X Configuration 4.3

## NOTE: Do not change format of CONFIG_SECTION_{BGN,END} comments!

## CONFIG_SECTION_BGN=cluster ==================================================

## Cluster name.
##
## Value: String
cluster.name = dc3-emqx-cluster

## Specify the erlang distributed protocol.
##
## Value: Enum
##  - inet_tcp: the default; handles TCP streams with IPv4 addressing.
##  - inet6_tcp: handles TCP with IPv6 addressing.
##  - inet_tls: using TLS for Erlang Distribution.
##
## vm.args: -proto_dist inet_tcp
cluster.proto_dist = inet_tcp

## Cluster auto-discovery strategy.
##
## Value: Enum
## - manual: Manual join command
## - static: Static node list
## - mcast:  IP Multicast
## - dns:    DNS A Record
## - etcd:   etcd
## - k8s:    Kubernetes
##
## Default: manual
cluster.discovery = manual

## Enable cluster autoheal from network partition.
##
## Value: on | off
##
## Default: on
cluster.autoheal = on

## Autoclean down node. A down node will be removed from the cluster
## if this value > 0.
##
## Value: Duration
## -h: hour, e.g. '2h' for 2 hours
## -m: minute, e.g. '5m' for 5 minutes
## -s: second, e.g. '30s' for 30 seconds
##
## Default: 5m
cluster.autoclean = 5m

##--------------------------------------------------------------------
## Cluster using static node list

## Node list of the cluster.
##
## Value: String
## cluster.static.seeds = emqx1@127.0.0.1,emqx2@127.0.0.1

##--------------------------------------------------------------------
## Cluster using IP Multicast.

## IP Multicast Address.
##
## Value: IP Address
## cluster.mcast.addr = ***********

## Multicast Ports.
##
## Value: Port List
## cluster.mcast.ports = 4369,4370

## Multicast Iface.
##
## Value: Iface Address
##
## Default: 0.0.0.0
## cluster.mcast.iface = 0.0.0.0

## Multicast Ttl.
##
## Value: 0-255
## cluster.mcast.ttl = 255

## Multicast loop.
##
## Value: on | off
## cluster.mcast.loop = on

##--------------------------------------------------------------------
## Cluster using DNS A records.

## DNS name.
##
## Value: String
## cluster.dns.name = localhost

## The App name is used to build 'node.name' with IP address.
##
## Value: String
## cluster.dns.app = emqx

##--------------------------------------------------------------------
## Cluster using etcd

## Etcd server list, seperated by ','.
##
## Value: String
## cluster.etcd.server = http://127.0.0.1:2379

## The prefix helps build nodes path in etcd. Each node in the cluster
## will create a path in etcd: v2/keys/<prefix>/<cluster.name>/<node.name>
##
## Value: String
## cluster.etcd.prefix = dc3-emqx-cluster

## The TTL for node's path in etcd.
##
## Value: Duration
##
## Default: 1m, 1 minute
## cluster.etcd.node_ttl = 1m

## Path to a file containing the client's private PEM-encoded key.
##
## Value: File
## cluster.etcd.ssl.keyfile = etc/certs/client-key.pem

## The path to a file containing the client's certificate.
##
## Value: File
## cluster.etcd.ssl.certfile = etc/certs/client.pem

## Path to the file containing PEM-encoded CA certificates. The CA certificates
## are used during server authentication and when building the client certificate chain.
##
## Value: File
## cluster.etcd.ssl.cacertfile = etc/certs/ca.pem

##--------------------------------------------------------------------
## Cluster using Kubernetes

## Kubernetes API server list, seperated by ','.
##
## Value: String
## cluster.k8s.apiserver = http://**************:8080

## The service name helps lookup EMQ nodes in the cluster.
##
## Value: String
## cluster.k8s.service_name = emqx

## The address type is used to extract host from k8s service.
##
## Value: ip | dns | hostname
## cluster.k8s.address_type = ip

## The app name helps build 'node.name'.
##
## Value: String
## cluster.k8s.app_name = emqx

## The suffix added to dns and hostname get from k8s service
##
## Value: String
## cluster.k8s.suffix = pod.cluster.local

## Kubernetes Namespace
##
## Value: String
## cluster.k8s.namespace = default

## CONFIG_SECTION_END=cluster ==================================================

##--------------------------------------------------------------------
## Node
##--------------------------------------------------------------------

## Node name.
##
## See: http://erlang.org/doc/reference_manual/distributed.html
##
## <AUTHOR> <EMAIL>
##
## Default: node-01@127.0.0.1
node.name = node-01@127.0.0.1

## Cookie for distributed node communication.
##
## Value: String
node.cookie = emqxsecretcookie

## Data dir for the node
##
## Value: Folder
node.data_dir = data

## Heartbeat monitoring of an Erlang runtime system. Comment the line to disable
## heartbeat, or set the value as 'on'
##
## Turning this on may cause the node to restart if it becomes unresponsive to
## the heartbeat pings.
##
## NOTE: When managed by systemd (or other supervision tools like systemd),
##       heart will probably only cause EMQ X to stop, but restart or not will
##       depend on systemd's restart strategy.
## NOTE: When running in docker, the container will die as soon as the the
##       heart process kills EMQ X, but restart or not will depend on container
##       supervision strategy, such as k8s restartPolicy.
##
## Value: on
##
## vm.args: -heart
## node.heartbeat = on

## Sets the number of threads in async thread pool. Valid range is 0-1024.
##
## See: http://erlang.org/doc/man/erl.html
##
## Value: 0-1024
##
## vm.args: +A Number
## node.async_threads = 4

## Sets the maximum number of simultaneously existing processes for this
## system if a Number is passed as value.
##
## See: http://erlang.org/doc/man/erl.html
##
## Value: Number [1024-134217727]
##
## vm.args: +P Number
## node.process_limit = 2097152

## Sets the maximum number of simultaneously existing ports for this system.
##
## See: http://erlang.org/doc/man/erl.html
##
## Value: Number [1024-134217727]
##
## vm.args: +Q Number
## node.max_ports = 1048576

## Sets the distribution buffer busy limit (dist_buf_busy_limit).
##
## See: http://erlang.org/doc/man/erl.html
##
## Value: Number [1KB-2GB]
##
## vm.args: +zdbbl size
## node.dist_buffer_size = 8MB

## Sets the maximum number of ETS tables. Note that mnesia and SSL will
## create temporary ETS tables.
##
## Value: Number
##
## vm.args: +e Number
## node.max_ets_tables = 262144

## Global GC Interval.
##
## Value: Duration
##
## Examples:
##  - 2h:  2 hours
##  - 30m: 30 minutes
##  - 20s: 20 seconds
##
## Defaut: 15 minutes
node.global_gc_interval = 15m

## Tweak GC to run more often.
##
## Value: Number [0-65535]
##
## vm.args: -env ERL_FULLSWEEP_AFTER Number
## node.fullsweep_after = 1000

## Crash dump log file.
##
## Value: Log file
node.crash_dump = log/crash.dump

## Specify SSL Options in the file if using SSL for Erlang Distribution.
##
## Value: File
##
## vm.args: -ssl_dist_optfile <File>
## node.ssl_dist_optfile = etc/ssl_dist.conf

## Sets the net_kernel tick time. TickTime is specified in seconds.
## Notice that all communicating nodes are to have the same TickTime
## value specified.
##
## See: http://www.erlang.org/doc/man/kernel_app.html#net_ticktime
##
## Value: Number
##
## vm.args: -kernel net_ticktime Number
## node.dist_net_ticktime = 120

## Sets the port range for the listener socket of a distributed Erlang node.
## Note that if there are firewalls between clustered nodes, this port segment
## for nodes’ communication should be allowed.
##
## See: http://www.erlang.org/doc/man/kernel_app.html
##
## Value: Port [1024-65535]
node.dist_listen_min = 6369
node.dist_listen_max = 6369

node.backtrace_depth = 16

## CONFIG_SECTION_BGN=rpc ======================================================

## RPC Mode.
##
## Value: sync | async
rpc.mode = async

## Max batch size of async RPC requests.
##
## Value: Integer
## Zero or negative value disables rpc batching.
##
## NOTE: RPC batch won't work when rpc.mode = sync
rpc.async_batch_size = 256

## RPC port discovery
##
## The strategy for discovering the RPC listening port of other nodes.
##
## Value: Enum
## - manual: discover ports by `tcp_server_port` and `tcp_client_port`.
## - stateless: discover ports in a stateless manner.
##   If node name is `emqx<N>@127.0.0.1`, where the `<N>` is an integer,
##   then the listening port will be `5370 + <N>`
##
## Defaults to `stateless`.
rpc.port_discovery = stateless

## TCP port number for RPC server to listen on.
##
## Only takes effect when `rpc.port_discovery` = `manual`.
##
## NOTE: All nodes in the cluster should agree to this same config.
##
## Value: Port [1024-65535]
#rpc.tcp_server_port = 5369

## Number of outgoing RPC connections.
##
## Value: Interger [0-256]
## Default = 1
#rpc.tcp_client_num = 1

## RCP Client connect timeout.
##
## Value: Seconds
rpc.connect_timeout = 5s

## TCP send timeout of RPC client and server.
##
## Value: Seconds
rpc.send_timeout = 5s

## Authentication timeout
##
## Value: Seconds
rpc.authentication_timeout = 5s

## Default receive timeout for call() functions
##
## Value: Seconds
rpc.call_receive_timeout = 15s

## Socket idle keepalive.
##
## Value: Seconds
rpc.socket_keepalive_idle = 900s

## TCP Keepalive probes interval.
##
## Value: Seconds
rpc.socket_keepalive_interval = 75s

## Probes lost to close the connection
##
## Value: Integer
rpc.socket_keepalive_count = 9

## Size of TCP send buffer.
##
## Value: Bytes
rpc.socket_sndbuf = 1MB

## Size of TCP receive buffer.
##
## Value: Seconds
rpc.socket_recbuf = 1MB

## Size of user-level software socket buffer.
##
## Value: Seconds
rpc.socket_buffer = 1MB

## CONFIG_SECTION_END=rpc ======================================================

## CONFIG_SECTION_BGN=logger ===================================================

## Where to emit the logs.
## Enable the console (standard output) logs.
##
## Value: file | console | both
## - file: write logs only to file
## - console: write logs only to standard I/O
## - both: write logs both to file and standard I/O
log.to = file

## The log severity level.
##
## Value: debug | info | notice | warning | error | critical | alert | emergency
##
## Note: Only the messages with severity level higher than or equal to
##       this level will be logged.
##
## Default: warning
log.level = warning

## The dir for log files.
##
## Value: Folder
log.dir = log

## The log filename for logs of level specified in "log.level".
##
## If `log.rotation` is enabled, this is the base name of the
## files. Each file in a rotated log is named <base_name>.N, where N is an integer.
##
## Value: String
## Default: emqx.log
log.file = emqx.log

## Limits the total number of characters printed for each log event.
##
## Value: Integer
## Default: No Limit
#log.chars_limit = 8192

## Maximum depth for Erlang term log formatting
## and Erlang process message queue inspection.
##
## Value: Integer or 'unlimited' (without quotes)
## Default: 100
#log.max_depth = 100

## Log formatter
## Value: text | json
#log.formatter = text

## Log to single line
## Value: Boolean
#log.single_line = true

## Enables the log rotation.
## With this enabled, new log files will be created when the current
## log file is full, max to `log.rotation.size` files will be created.
##
## Value: on | off
## Default: on
log.rotation = on

## Maximum size of each log file.
##
## Value: Number
## Default: 10M
## Supported Unit: KB | MB | GB
log.rotation.size = 10MB

## Maximum rotation count of log files.
##
## Value: Number
## Default: 5
log.rotation.count = 5

## To create additional log files for specific log levels.
##
## Value: File Name
## Format: log.$level.file = $filename,
##         where "$level" can be one of: debug, info, notice, warning,
##                                       error, critical, alert, emergency
## Note: Log files for a specific log level will only contain all the logs
##       that higher than or equal to that level
##
#log.info.file  = info.log
#log.error.file = error.log

## The max allowed queue length before switching to sync mode.
##
## Log overload protection parameter. If the message queue grows
## larger than this value the handler switches from anync to sync mode.
##
## Default: 100
##
#log.sync_mode_qlen = 100

## The max allowed queue length before switching to drop mode.
##
## Log overload protection parameter. When the message queue grows
## larger than this threshold, the handler switches to a mode in which
## it drops all new events that senders want to log.
##
## Default: 3000
##
#log.drop_mode_qlen = 3000

## The max allowed queue length before switching to flush mode.
##
## Log overload protection parameter. If the length of the message queue
## grows larger than this threshold, a flush (delete) operation takes place.
## To flush events, the handler discards the messages in the message queue
## by receiving them in a loop without logging.
##
## Default: 8000
##
#log.flush_qlen = 8000

## Kill the log handler when it gets overloaded.
##
## Log overload protection parameter. It is possible that a handler,
## even if it can successfully manage peaks of high load without crashing,
## can build up a large message queue, or use a large amount of memory.
## We could kill the log handler in these cases and restart it after a
## few seconds.
##
## Default: on
##
#log.overload_kill = on

## The max allowed queue length before killing the log hanlder.
##
## Log overload protection parameter. This is the maximum allowed queue
## length. If the message queue grows larger than this, the handler
## process is terminated.
##
## Default: 20000
##
#log.overload_kill_qlen = 20000

## The max allowed memory size before killing the log hanlder.
##
## Log overload protection parameter. This is the maximum memory size
## that the handler process is allowed to use. If the handler grows
## larger than this, the process is terminated.
##
## Default: 30MB
##
#log.overload_kill_mem_size = 30MB

## Restart the log hanlder after some seconds.
##
## Log overload protection parameter. If the handler is terminated,
## it restarts automatically after a delay specified in seconds.
## The value "infinity" prevents restarts.
##
## Default: 5s
##
#log.overload_kill_restart_after = 5s

## Max burst count and time window for burst control.
##
## Log overload protection parameter. Large bursts of log events - many
## events received by the handler under a short period of time - can
## potentially cause problems. By specifying the maximum number of events
## to be handled within a certain time frame, the handler can avoid
## choking the log with massive amounts of printouts.
##
## This config controls the maximum number of events to handle within
## a time frame. After the limit is reached, successive events are
## dropped until the end of the time frame.
##
## Note that there would be no warning if any messages were
## dropped because of burst control.
##
## Comment this config out to disable the burst control feature.
##
## Value: MaxBurstCount,TimeWindow
## Default: disabled
##
#log.burst_limit = 20000, 1s

## CONFIG_SECTION_END=logger ===================================================

##--------------------------------------------------------------------
## Authentication/Access Control
##--------------------------------------------------------------------

## Allow anonymous authentication by default if no auth plugins loaded.
## Notice: Disable the option in production deployment!
##
## Value: true | false
allow_anonymous = true

## Allow or deny if no ACL rules matched.
##
## Value: allow | deny
acl_nomatch = allow

## Default ACL File.
##
## Value: File Name
acl_file = etc/acl.conf

## Whether to enable ACL cache.
##
## If enabled, ACLs roles for each client will be cached in the memory
##
## Value: on | off
enable_acl_cache = on

## The maximum count of ACL entries can be cached for a client.
##
## Value: Integer greater than 0
## Default: 32
acl_cache_max_size = 32

## The time after which an ACL cache entry will be deleted
##
## Value: Duration
## Default: 1 minute
acl_cache_ttl = 1m

## The action when acl check reject current operation
##
## Value: ignore | disconnect
## Default: ignore
acl_deny_action = ignore

## Specify the global flapping detect policy.
## The value is a string composed of flapping threshold, duration and banned interval.
## 1. threshold: an integer to specfify the disconnected times of a MQTT Client;
## 2. duration: the time window for flapping detect;
## 3. banned interval: the banned interval if a flapping is detected.
##
## Value: Integer,Duration,Duration
flapping_detect_policy = 30, 1m, 5m

##--------------------------------------------------------------------
## MQTT Protocol
##--------------------------------------------------------------------

## Maximum MQTT packet size allowed.
##
## Value: Bytes
## Default: 1MB
mqtt.max_packet_size = 1MB

## Maximum length of MQTT clientId allowed.
##
## Value: Number [23-65535]
mqtt.max_clientid_len = 65535

## Maximum topic levels allowed. 0 means no limit.
##
## Value: Number
mqtt.max_topic_levels = 0

## Maximum QoS allowed.
##
## Value: 0 | 1 | 2
mqtt.max_qos_allowed = 2

## Maximum Topic Alias, 0 means no topic alias supported.
##
## Value: 0-65535
mqtt.max_topic_alias = 65535

## Whether the Server supports MQTT retained messages.
##
## Value: boolean
mqtt.retain_available = true

## Whether the Server supports MQTT Wildcard Subscriptions
##
## Value: boolean
mqtt.wildcard_subscription = true

## Whether the Server supports MQTT Shared Subscriptions.
##
## Value: boolean
mqtt.shared_subscription = true

## Whether to ignore loop delivery of messages.(for mqtt v3.1.1)
##
## Value: true | false
mqtt.ignore_loop_deliver = false

## Whether to parse the MQTT frame in strict mode
##
## Value: true | false
mqtt.strict_mode = false

## Specify the response information returned to the client
##
## Value: String
## mqtt.response_information = example

## CONFIG_SECTION_BGN=zones  ===================================================

##--------------------------------------------------------------------
## External Zone

## Idle timeout of the external MQTT connections.
##
## Value: duration
zone.external.idle_timeout = 15s

## Enable ACL check.
##
## Value: Flag
zone.external.enable_acl = on

## Enable ban check.
##
## Value: Flag
zone.external.enable_ban = on

## Enable per connection statistics.
##
## Value: on | off
zone.external.enable_stats = on

## The action when acl check reject current operation
##
## Value: ignore | disconnect
## Default: ignore
zone.external.acl_deny_action = ignore

## Force the MQTT connection process GC after this number of
## messages | bytes passed through.
##
## Numbers delimited by `|'. Zero or negative is to disable.
zone.external.force_gc_policy = 16000|16MB

## Max message queue length and total heap size to force shutdown
## connection/session process.
## Message queue here is the Erlang process mailbox, but not the number
## of queued MQTT messages of QoS 1 and 2.
##
## Numbers delimited by `|'. Zero or negative is to disable.
##
## Default:
##   - 10000|64MB on ARCH_64 system
##   - 1000|32MB  on ARCH_32 sytem
#zone.external.force_shutdown_policy = 10000|64MB

## Maximum MQTT packet size allowed.
##
## Value: Bytes
## Default: 1MB
## zone.external.max_packet_size = 64KB

## Maximum length of MQTT clientId allowed.
##
## Value: Number [23-65535]
## zone.external.max_clientid_len = 1024

## Maximum topic levels allowed. 0 means no limit.
##
## Value: Number
## zone.external.max_topic_levels = 7

## Maximum QoS allowed.
##
## Value: 0 | 1 | 2
## zone.external.max_qos_allowed = 2

## Maximum Topic Alias, 0 means no limit.
##
## Value: 0-65535
## zone.external.max_topic_alias = 65535

## Whether the Server supports retained messages.
##
## Value: boolean
## zone.external.retain_available = true

## Whether the Server supports Wildcard Subscriptions
##
## Value: boolean
## zone.external.wildcard_subscription = false

## Whether the Server supports Shared Subscriptions
##
## Value: boolean
## zone.external.shared_subscription = false

## Server Keep Alive
##
## Value: Number
## zone.external.server_keepalive = 0

## The backoff for MQTT keepalive timeout. The broker will kick a connection out
## until 'Keepalive * backoff * 2' timeout.
##
## Value: Float > 0.5
zone.external.keepalive_backoff = 0.75

## Maximum number of subscriptions allowed, 0 means no limit.
##
## Value: Number
zone.external.max_subscriptions = 0

## Force to upgrade QoS according to subscription.
##
## Value: on | off
zone.external.upgrade_qos = off

## Maximum size of the Inflight Window storing QoS1/2 messages delivered but unacked.
##
## Value: Number
zone.external.max_inflight = 32

## Retry interval for QoS1/2 message delivering.
##
## Value: Duration
zone.external.retry_interval = 30s

## Maximum QoS2 packets (Client -> Broker) awaiting PUBREL, 0 means no limit.
##
## Value: Number
zone.external.max_awaiting_rel = 100

## The QoS2 messages (Client -> Broker) will be dropped if awaiting PUBREL timeout.
##
## Value: Duration
zone.external.await_rel_timeout = 300s

## Default session expiry interval for MQTT V3.1.1 connections.
##
## Value: Duration
## -d: day
## -h: hour
## -m: minute
## -s: second
##
## Default: 2h, 2 hours
zone.external.session_expiry_interval = 2h

## Maximum queue length. Enqueued messages when persistent client disconnected,
## or inflight window is full. 0 means no limit.
##
## Value: Number >= 0
zone.external.max_mqueue_len = 1000

## Topic priorities.
## 'none' to indicate no priority table (by default), hence all messages
## are treated equal
##
## Priority number [1-255]
## Example: topic/1=10,topic/2=8
## NOTE: comma and equal signs are not allowed for priority topic names
## NOTE: messages for topics not in the priority table are treated as
##       either highest or lowest priority depending on the configured
##       value for mqueue_default_priority
##
zone.external.mqueue_priorities = none

## Default to highest priority for topics not matching priority table
##
## Value: highest | lowest
zone.external.mqueue_default_priority = highest

## Whether to enqueue QoS0 messages.
##
## Value: false | true
zone.external.mqueue_store_qos0 = true

## Whether to turn on flapping detect
##
## Value: on | off
zone.external.enable_flapping_detect = off

## Message limit for the a external MQTT connection.
##
## Value: Number,Duration
## Example: 100 messages per 10 seconds.
#zone.external.rate_limit.conn_messages_in = 100,10s

## Bytes limit for a external MQTT connections.
##
## Value: Number,Duration
## Example: 100KB incoming per 10 seconds.
#zone.external.rate_limit.conn_bytes_in = 100KB,10s

## Whether to alarm the congested connections.
##
## Sometimes the mqtt connection (usually an MQTT subscriber) may get "congested" because
## there're too many packets to sent. The socket trys to buffer the packets until the buffer is
## full. If more packets comes after that, the packets will be "pending" in a queue
## and we consider the connection is "congested".
##
## Enable this to send an alarm when there's any bytes pending in the queue. You could set
## the `listener.tcp.<ZoneName>.sndbuf` to a larger value if the alarm is triggered too often.
##
## The name of the alarm is of format "conn_congestion/<ClientID>/<Username>".
## Where the <ClientID> is the client-id of the congested MQTT connection.
## And the <Username> is the username or "unknown_user" of not provided by the client.
## Default: off
#zone.external.conn_congestion.alarm = off

## Won't clear the congested alarm in how long time.
## The alarm is cleared only when there're no pending bytes in the queue, and also it has been
## `min_alarm_sustain_duration` time since the last time we considered the connection is "congested".
##
## This is to avoid clearing and sending the alarm again too often.
## Default: 1m
#zone.external.conn_congestion.min_alarm_sustain_duration = 1m

## Messages quota for the each of external MQTT connection.
## This value consumed by the number of recipient on a message.
##
## Value: Number, Duration
##
## Example: 100 messaegs per 1s
#zone.external.quota.conn_messages_routing = 100,1s

## Messages quota for the all of external MQTT connections.
## This value consumed by the number of recipient on a message.
##
## Value: Number, Duration
##
## Example: 200000 messaegs per 1s
#zone.external.quota.overall_messages_routing = 200000,1s

## All the topics will be prefixed with the mountpoint path if this option is enabled.
##
## Variables in mountpoint path:
##  - %c: clientid
##  - %u: username
##
## Value: String
## zone.external.mountpoint = devicebound/

## Whether use username replace client id
##
## Value: boolean
## Default: false
zone.external.use_username_as_clientid = false

## Whether to ignore loop delivery of messages.(for mqtt v3.1.1)
##
## Value: true | false
zone.external.ignore_loop_deliver = false

## Whether to parse the MQTT frame in strict mode
##
## Value: true | false
zone.external.strict_mode = false

## Specify the response information returned to the client
##
## Value: String
## zone.external.response_information = example

##--------------------------------------------------------------------
## Internal Zone

zone.internal.allow_anonymous = true

## Enable per connection stats.
##
## Value: Flag
zone.internal.enable_stats = on

## Enable ACL check.
##
## Value: Flag
zone.internal.enable_acl = off

## The action when acl check reject current operation
##
## Value: ignore | disconnect
## Default: ignore
zone.internal.acl_deny_action = ignore

## See zone.$name.force_gc_policy
## zone.internal.force_gc_policy = 128000|128MB

## See zone.$name.wildcard_subscription.
##
## Value: boolean
## zone.internal.wildcard_subscription = true

## See zone.$name.shared_subscription.
##
## Value: boolean
## zone.internal.shared_subscription = true

## See zone.$name.max_subscriptions.
##
## Value: Integer
zone.internal.max_subscriptions = 0

## See zone.$name.max_inflight
##
## Value: Number
zone.internal.max_inflight = 128

## See zone.$name.max_awaiting_rel
##
## Value: Number
zone.internal.max_awaiting_rel = 1000

## See zone.$name.max_mqueue_len
##
## Value: Number >= 0
zone.internal.max_mqueue_len = 10000

## Whether to enqueue Qos0 messages.
##
## Value: false | true
zone.internal.mqueue_store_qos0 = true

## Whether to turn on flapping detect
##
## Value: on | off
zone.internal.enable_flapping_detect = off

## See zone.$name.force_shutdown_policy
##
## Default:
##   - 10000|64MB on ARCH_64 system
##   - 1000|32MB  on ARCH_32 sytem
#zone.internal.force_shutdown_policy = 10000|64MB

## All the topics will be prefixed with the mountpoint path if this option is enabled.
##
## Variables in mountpoint path:
##  - %c: clientid
##  - %u: username
##
## Value: String
## zone.internal.mountpoint = cloudbound/

## Whether to ignore loop delivery of messages.(for mqtt v3.1.1)
##
## Value: true | false
zone.internal.ignore_loop_deliver = false

## Whether to parse the MQTT frame in strict mode
##
## Value: true | false
zone.internal.strict_mode = false

## Specify the response information returned to the client
##
## Value: String
## zone.internal.response_information = example

## Allow the zone's clients to bypass authentication step
##
## Value: true | false
zone.internal.bypass_auth_plugins = true

## CONFIG_SECTION_END=zones ====================================================

## CONFIG_SECTION_BGN=listeners ================================================

##--------------------------------------------------------------------
## MQTT/TCP - External TCP Listener for MQTT Protocol

## listener.tcp.$name is the IP address and port that the MQTT/TCP
## listener will bind.
##
## Value: IP:Port | Port
##
## Examples: 3000, 127.0.0.1:3000, ::1:3000
listener.tcp.external = 3000

## The acceptor pool for external MQTT/TCP listener.
##
## Value: Number
listener.tcp.external.acceptors = 8

## Maximum number of concurrent MQTT/TCP connections.
##
## Value: Number
listener.tcp.external.max_connections = 1024000

## Maximum external connections per second.
##
## Value: Number
listener.tcp.external.max_conn_rate = 1000

## Specify the {active, N} option for the external MQTT/TCP Socket.
##
## Value: Number
listener.tcp.external.active_n = 100

## Zone of the external MQTT/TCP listener belonged to.
##
## See: zone.$name.*
##
## Value: String
listener.tcp.external.zone = external

## The access control rules for the MQTT/TCP listener.
##
## See: https://github.com/emqtt/esockd#allowdeny
##
## Value: ACL Rule
##
## Example: allow ***********/24
listener.tcp.external.access.1 = allow all

## Enable the Proxy Protocol V1/2 if the EMQ X cluster is deployed
## behind HAProxy or Nginx.
##
## See: https://www.haproxy.com/blog/haproxy/proxy-protocol/
##
## Value: on | off
## listener.tcp.external.proxy_protocol = on

## Sets the timeout for proxy protocol. EMQ X will close the TCP connection
## if no proxy protocol packet recevied within the timeout.
##
## Value: Duration
## listener.tcp.external.proxy_protocol_timeout = 3s

## Enable the option for X.509 certificate based authentication.
## EMQX will use the common name of certificate as MQTT username.
## Only support Proxy Protocol V2, the CN is available in Proxy Protocol V2 additional info
##
## Value: cn
## listener.tcp.external.peer_cert_as_username = cn

## Enable the option for X.509 certificate based authentication.
## EMQX will use the common name of certificate as MQTT clientid.
## Only support Proxy Protocol V2, the CN is available in Proxy Protocol V2 additional info
##
## Value: cn
## listener.tcp.external.peer_cert_as_clientid = cn

## The TCP backlog defines the maximum length that the queue of pending
## connections can grow to.
##
## Value: Number >= 0
listener.tcp.external.backlog = 1024

## The TCP send timeout for external MQTT connections.
##
## Value: Duration
listener.tcp.external.send_timeout = 15s

## Close the TCP connection if send timeout.
##
## Value: on | off
listener.tcp.external.send_timeout_close = on

## The TCP receive buffer(os kernel) for MQTT connections.
##
## See: http://erlang.org/doc/man/inet.html
##
## Value: Bytes
## listener.tcp.external.recbuf = 2KB

## The TCP send buffer(os kernel) for MQTT connections.
##
## See: http://erlang.org/doc/man/inet.html
##
## Value: Bytes
## listener.tcp.external.sndbuf = 2KB

## The size of the user-level software buffer used by the driver.
## Not to be confused with options sndbuf and recbuf, which correspond
## to the Kernel socket buffers. It is recommended to have val(buffer)
## >= max(val(sndbuf),val(recbuf)) to avoid performance issues because
## of unnecessary copying. val(buffer) is automatically set to the above
## maximum when values sndbuf or recbuf are set.
##
## See: http://erlang.org/doc/man/inet.html
##
## Value: Bytes
## listener.tcp.external.buffer = 2KB

## Sets the 'buffer = max(sndbuf, recbuf)' if this option is enabled.
##
## Value: on | off
## listener.tcp.external.tune_buffer = off

## The socket is set to a busy state when the amount of data queued internally
## by the ERTS socket implementation reaches this limit.
##
## Value: on | off
## Defaults to 1MB
## listener.tcp.external.high_watermark = 1MB

## The TCP_NODELAY flag for MQTT connections. Small amounts of data are
## sent immediately if the option is enabled.
##
## Value: true | false
listener.tcp.external.nodelay = true

## The SO_REUSEADDR flag for TCP listener.
##
## Value: true | false
listener.tcp.external.reuseaddr = true

##--------------------------------------------------------------------
## Internal TCP Listener for MQTT Protocol

## The IP address and port that the internal MQTT/TCP protocol listener
## will bind.
##
## Value: IP:Port, Port
##
## Examples: 13000, 127.0.0.1:13000, ::1:13000
listener.tcp.internal = 13000

## The acceptor pool for internal MQTT/TCP listener.
##
## Value: Number
listener.tcp.internal.acceptors = 4

## Maximum number of concurrent MQTT/TCP connections.
##
## Value: Number
listener.tcp.internal.max_connections = 1024000

## Maximum internal connections per second.
##
## Value: Number
listener.tcp.internal.max_conn_rate = 1000

## Specify the {active, N} option for the internal MQTT/TCP Socket.
##
## Value: Number
listener.tcp.internal.active_n = 1000

## Zone of the internal MQTT/TCP listener belonged to.
##
## Value: String
listener.tcp.internal.zone = internal

## The TCP backlog of internal MQTT/TCP Listener.
##
## See: listener.tcp.$name.backlog
##
## Value: Number >= 0
listener.tcp.internal.backlog = 512

## The TCP send timeout for internal MQTT connections.
##
## See: listener.tcp.$name.send_timeout
##
## Value: Duration
listener.tcp.internal.send_timeout = 5s

## Close the MQTT/TCP connection if send timeout.
##
## See: listener.tcp.$name.send_timeout_close
##
## Value: on | off
listener.tcp.internal.send_timeout_close = on

## The TCP receive buffer(os kernel) for internal MQTT connections.
##
## See: listener.tcp.$name.recbuf
##
## Value: Bytes
listener.tcp.internal.recbuf = 64KB

## The TCP send buffer(os kernel) for internal MQTT connections.
##
## See: http://erlang.org/doc/man/inet.html
##
## Value: Bytes
listener.tcp.internal.sndbuf = 64KB

## The size of the user-level software buffer used by the driver.
##
## See: listener.tcp.$name.buffer
##
## Value: Bytes
## listener.tcp.internal.buffer = 16KB

## Sets the 'buffer = max(sndbuf, recbuf)' if this option is enabled.
##
## See: listener.tcp.$name.tune_buffer
##
## Value: on | off
## listener.tcp.internal.tune_buffer = off

## The TCP_NODELAY flag for internal MQTT connections.
##
## See: listener.tcp.$name.nodelay
##
## Value: true | false
listener.tcp.internal.nodelay = false

## The SO_REUSEADDR flag for MQTT/TCP Listener.
##
## Value: true | false
listener.tcp.internal.reuseaddr = true

##--------------------------------------------------------------------
## MQTT/SSL - External SSL Listener for MQTT Protocol

## listener.ssl.$name is the IP address and port that the MQTT/SSL
## listener will bind.
##
## Value: IP:Port | Port
##
## Examples: 3010, 127.0.0.1:3010, ::1:3010
listener.ssl.external = 3010

## The acceptor pool for external MQTT/SSL listener.
##
## Value: Number
listener.ssl.external.acceptors = 16

## Maximum number of concurrent MQTT/SSL connections.
##
## Value: Number
listener.ssl.external.max_connections = 102400

## Maximum MQTT/SSL connections per second.
##
## Value: Number
listener.ssl.external.max_conn_rate = 500

## Specify the {active, N} option for the internal MQTT/SSL Socket.
##
## Value: Number
listener.ssl.external.active_n = 100

## Zone of the external MQTT/SSL listener belonged to.
##
## Value: String
listener.ssl.external.zone = external

## The access control rules for the MQTT/SSL listener.
##
## See: listener.tcp.$name.access
##
## Value: ACL Rule
listener.ssl.external.access.1 = allow all

## Enable the Proxy Protocol V1/2 if the EMQ cluster is deployed behind
## HAProxy or Nginx.
##
## See: listener.tcp.$name.proxy_protocol
##
## Value: on | off
## listener.ssl.external.proxy_protocol = on

## Sets the timeout for proxy protocol.
##
## See: listener.tcp.$name.proxy_protocol_timeout
##
## Value: Duration
## listener.ssl.external.proxy_protocol_timeout = 3s

## TLS versions only to protect from POODLE attack.
##
## See: http://erlang.org/doc/man/ssl.html
##
## Value: String, seperated by ','
## NOTE: Do not use tlsv1.3 if emqx is running on OTP-22 or earlier
## listener.ssl.external.tls_versions = tlsv1.3,tlsv1.2,tlsv1.1,tlsv1

## TLS Handshake timeout.
##
## Value: Duration
listener.ssl.external.handshake_timeout = 15s

## Maximum number of non-self-issued intermediate certificates that
## can follow the peer certificate in a valid certification path.
##
## Value: Number
## listener.ssl.external.depth = 10

## String containing the user's password. Only used if the private keyfile
## is password-protected.
##
## Value: String
## listener.ssl.external.key_password = yourpass

## Path to the file containing the user's private PEM-encoded key.
##
## See: http://erlang.org/doc/man/ssl.html
##
## Value: File
listener.ssl.external.keyfile = etc/certs/key.pem

## Path to a file containing the user certificate.
##
## See: http://erlang.org/doc/man/ssl.html
##
## Value: File
listener.ssl.external.certfile = etc/certs/cert.pem

## Path to the file containing PEM-encoded CA certificates. The CA certificates
## are used during server authentication and when building the client certificate chain.
##
## Value: File
## listener.ssl.external.cacertfile = etc/certs/cacert.pem

## The Ephemeral Diffie-Helman key exchange is a very effective way of
## ensuring Forward Secrecy by exchanging a set of keys that never hit
## the wire. Since the DH key is effectively signed by the private key,
## it needs to be at least as strong as the private key. In addition,
## the default DH groups that most of the OpenSSL installations have
## are only a handful (since they are distributed with the OpenSSL
## package that has been built for the operating system it’s running on)
## and hence predictable (not to mention, 1024 bits only).
## In order to escape this situation, first we need to generate a fresh,
## strong DH group, store it in a file and then use the option above,
## to force our SSL application to use the new DH group. Fortunately,
## OpenSSL provides us with a tool to do that. Simply run:
## openssl dhparam -out dh-params.pem 2048
##
## Value: File
## listener.ssl.external.dhfile = etc/certs/dh-params.pem

## A server only does x509-path validation in mode verify_peer,
## as it then sends a certificate request to the client (this
## message is not sent if the verify option is verify_none).
## You can then also want to specify option fail_if_no_peer_cert.
## More information at: http://erlang.org/doc/man/ssl.html
##
## Value: verify_peer | verify_none
## listener.ssl.external.verify = verify_peer

## Used together with {verify, verify_peer} by an SSL server. If set to true,
## the server fails if the client does not have a certificate to send, that is,
## sends an empty certificate.
##
## Value: true | false
## listener.ssl.external.fail_if_no_peer_cert = true

## This is the single most important configuration option of an Erlang SSL
## application. Ciphers (and their ordering) define the way the client and
## server encrypt information over the wire, from the initial Diffie-Helman
## key exchange, the session key encryption ## algorithm and the message
## digest algorithm. Selecting a good cipher suite is critical for the
## application’s data security, confidentiality and performance.
##
## The cipher list above offers:
##
## A good balance between compatibility with older browsers.
## It can get stricter for Machine-To-Machine scenarios.
## Perfect Forward Secrecy.
## No old/insecure encryption and HMAC algorithms
##
## Most of it was copied from Mozilla’s Server Side TLS article
##
## Value: Ciphers
listener.ssl.external.ciphers = TLS_AES_256_GCM_SHA384,TLS_AES_128_GCM_SHA256,TLS_CHACHA20_POLY1305_SHA256,TLS_AES_128_CCM_SHA256,TLS_AES_128_CCM_8_SHA256,ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-ECDSA-AES256-SHA384,ECDHE-RSA-AES256-SHA384,ECDHE-ECDSA-DES-CBC3-SHA,ECDH-ECDSA-AES256-GCM-SHA384,ECDH-RSA-AES256-GCM-SHA384,ECDH-ECDSA-AES256-SHA384,ECDH-RSA-AES256-SHA384,DHE-DSS-AES256-GCM-SHA384,DHE-DSS-AES256-SHA256,AES256-GCM-SHA384,AES256-SHA256,ECDHE-ECDSA-AES128-GCM-SHA256,ECDHE-RSA-AES128-GCM-SHA256,ECDHE-ECDSA-AES128-SHA256,ECDHE-RSA-AES128-SHA256,ECDH-ECDSA-AES128-GCM-SHA256,ECDH-RSA-AES128-GCM-SHA256,ECDH-ECDSA-AES128-SHA256,ECDH-RSA-AES128-SHA256,DHE-DSS-AES128-GCM-SHA256,DHE-DSS-AES128-SHA256,AES128-GCM-SHA256,AES128-SHA256,ECDHE-ECDSA-AES256-SHA,ECDHE-RSA-AES256-SHA,DHE-DSS-AES256-SHA,ECDH-ECDSA-AES256-SHA,ECDH-RSA-AES256-SHA,AES256-SHA,ECDHE-ECDSA-AES128-SHA,ECDHE-RSA-AES128-SHA,DHE-DSS-AES128-SHA,ECDH-ECDSA-AES128-SHA,ECDH-RSA-AES128-SHA,AES128-SHA


## Ciphers for TLS PSK.
## Note that 'listener.ssl.external.ciphers' and 'listener.ssl.external.psk_ciphers' cannot
## be configured at the same time.
## See 'https://tools.ietf.org/html/rfc4279#section-2'.
#listener.ssl.external.psk_ciphers = PSK-AES128-CBC-SHA,PSK-AES256-CBC-SHA,PSK-3DES-EDE-CBC-SHA,PSK-RC4-SHA

## SSL parameter renegotiation is a feature that allows a client and a server
## to renegotiate the parameters of the SSL connection on the fly.
## RFC 5746 defines a more secure way of doing this. By enabling secure renegotiation,
## you drop support for the insecure renegotiation, prone to MitM attacks.
##
## Value: on | off
## listener.ssl.external.secure_renegotiate = off

## A performance optimization setting, it allows clients to reuse
## pre-existing sessions, instead of initializing new ones.
## Read more about it here.
##
## See: http://erlang.org/doc/man/ssl.html
##
## Value: on | off
## listener.ssl.external.reuse_sessions = on

## An important security setting, it forces the cipher to be set based
## on the server-specified order instead of the client-specified order,
## hence enforcing the (usually more properly configured) security
## ordering of the server administrator.
##
## Value: on | off
## listener.ssl.external.honor_cipher_order = on

## Use the CN, DN or CRT field from the client certificate as a username.
## Notice that 'verify' should be set as 'verify_peer'.
## 'pem' encodes CRT in base64, and md5 is the md5 hash of CRT.
##
## Value: cn | dn | crt | pem | md5
## listener.ssl.external.peer_cert_as_username = cn

## Use the CN, DN or CRT field from the client certificate as a username.
## Notice that 'verify' should be set as 'verify_peer'.
## 'pem' encodes CRT in base64, and md5 is the md5 hash of CRT.
##
## Value: cn | dn | crt | pem | md5
## listener.ssl.external.peer_cert_as_clientid = cn

## TCP backlog for the SSL connection.
##
## See listener.tcp.$name.backlog
##
## Value: Number >= 0
## listener.ssl.external.backlog = 1024

## The TCP send timeout for the SSL connection.
##
## See listener.tcp.$name.send_timeout
##
## Value: Duration
## listener.ssl.external.send_timeout = 15s

## Close the SSL connection if send timeout.
##
## See: listener.tcp.$name.send_timeout_close
##
## Value: on | off
## listener.ssl.external.send_timeout_close = on

## The TCP receive buffer(os kernel) for the SSL connections.
##
## See: listener.tcp.$name.recbuf
##
## Value: Bytes
## listener.ssl.external.recbuf = 4KB

## The TCP send buffer(os kernel) for internal MQTT connections.
##
## See: listener.tcp.$name.sndbuf
##
## Value: Bytes
## listener.ssl.external.sndbuf = 4KB

## The size of the user-level software buffer used by the driver.
##
## See: listener.tcp.$name.buffer
##
## Value: Bytes
## listener.ssl.external.buffer = 4KB

## Sets the 'buffer = max(sndbuf, recbuf)' if this option is enabled.
##
## See: listener.tcp.$name.tune_buffer
##
## Value: on | off
## listener.ssl.external.tune_buffer = off

## The TCP_NODELAY flag for SSL connections.
##
## See: listener.tcp.$name.nodelay
##
## Value: true | false
## listener.ssl.external.nodelay = true

## The SO_REUSEADDR flag for MQTT/SSL Listener.
##
## Value: true | false
listener.ssl.external.reuseaddr = true

##--------------------------------------------------------------------
## External WebSocket listener for MQTT protocol

## listener.ws.$name is the IP address and port that the MQTT/WebSocket
## listener will bind.
##
## Value: IP:Port | Port
##
## Examples: 3020, 127.0.0.1:3020, ::1:3020
listener.ws.external = 3020

## The path of WebSocket MQTT endpoint
##
## Value: URL Path
listener.ws.external.mqtt_path = /mqtt

## The acceptor pool for external MQTT/WebSocket listener.
##
## Value: Number
listener.ws.external.acceptors = 4

## Maximum number of concurrent MQTT/WebSocket connections.
##
## Value: Number
listener.ws.external.max_connections = 102400

## Maximum MQTT/WebSocket connections per second.
##
## Value: Number
listener.ws.external.max_conn_rate = 1000

## Simulate the {active, N} option for the MQTT/WebSocket connections.
##
## Value: Number
listener.ws.external.active_n = 100

## Zone of the external MQTT/WebSocket listener belonged to.
##
## Value: String
listener.ws.external.zone = external

## The access control for the MQTT/WebSocket listener.
##
## See: listener.ws.$name.access
##
## Value: ACL Rule
listener.ws.external.access.1 = allow all

## If set to true, the server fails if the client does not have a Sec-WebSocket-Protocol to send.
## Set to false for WeChat MiniApp.
##
## Value: true | false
## listener.ws.external.fail_if_no_subprotocol = true

## Supported subprotocols
##
## Default: mqtt, mqtt-v3, mqtt-v3.1.1, mqtt-v5
## listener.ws.external.supported_subprotocols = mqtt, mqtt-v3, mqtt-v3.1.1, mqtt-v5

## Specify which HTTP header for real source IP if the EMQ X cluster is
## deployed behind NGINX or HAProxy.
##
## Default: X-Forwarded-For
## listener.ws.external.proxy_address_header = X-Forwarded-For

## Specify which HTTP header for real source port if the EMQ X cluster is
## deployed behind NGINX or HAProxy.
##
## Default: X-Forwarded-Port
## listener.ws.external.proxy_port_header = X-Forwarded-Port

## Enable the Proxy Protocol V1/2 if the EMQ cluster is deployed behind
## HAProxy or Nginx.
##
## See: listener.ws.$name.proxy_protocol
##
## Value: on | off
## listener.ws.external.proxy_protocol = on

## Sets the timeout for proxy protocol.
##
## See: listener.ws.$name.proxy_protocol_timeout
##
## Value: Duration
## listener.ws.external.proxy_protocol_timeout = 3s

## Enable the option for X.509 certificate based authentication.
## EMQX will use the common name of certificate as MQTT username.
## Only support Proxy Protocol V2, the CN is available in Proxy Protocol V2 additional info
##
## Value: cn
## listener.ws.external.peer_cert_as_username = cn

## Enable the option for X.509 certificate based authentication.
## EMQX will use the common name of certificate as MQTT clientid.
## Only support Proxy Protocol V2, the CN is available in Proxy Protocol V2 additional info
##
## Value: cn
## listener.ws.external.peer_cert_as_clientid = cn

## The TCP backlog of external MQTT/WebSocket Listener.
##
## See: listener.ws.$name.backlog
##
## Value: Number >= 0
listener.ws.external.backlog = 1024

## The TCP send timeout for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.send_timeout
##
## Value: Duration
listener.ws.external.send_timeout = 15s

## Close the MQTT/WebSocket connection if send timeout.
##
## See: listener.ws.$name.send_timeout_close
##
## Value: on | off
listener.ws.external.send_timeout_close = on

## The TCP receive buffer(os kernel) for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.recbuf
##
## Value: Bytes
## listener.ws.external.recbuf = 2KB

## The TCP send buffer(os kernel) for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.sndbuf
##
## Value: Bytes
## listener.ws.external.sndbuf = 2KB

## The size of the user-level software buffer used by the driver.
##
## See: listener.ws.$name.buffer
##
## Value: Bytes
## listener.ws.external.buffer = 2KB

## Sets the 'buffer = max(sndbuf, recbuf)' if this option is enabled.
##
## See: listener.ws.$name.tune_buffer
##
## Value: on | off
## listener.ws.external.tune_buffer = off

## The TCP_NODELAY flag for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.nodelay
##
## Value: true | false
listener.ws.external.nodelay = true

## The compress flag for external MQTT/WebSocket connections.
##
## If this Value is set true,the websocket message would be compressed
##
## Value: true | false
## listener.ws.external.compress = true

## The level of deflate options for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.deflate_opts.level
##
## Value: none | default | best_compression | best_speed
## listener.ws.external.deflate_opts.level = default

## The mem_level of deflate options for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.deflate_opts.mem_level
##
## Valid range is 1-9
## listener.ws.external.deflate_opts.mem_level = 8

## The strategy of deflate options for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.deflate_opts.strategy
##
## Value: default | filtered | huffman_only | rle
## listener.ws.external.deflate_opts.strategy = default

## The deflate option for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.deflate_opts.server_context_takeover
##
## Value: takeover | no_takeover
## listener.ws.external.deflate_opts.server_context_takeover = takeover

## The deflate option for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.deflate_opts.client_context_takeover
##
## Value: takeover | no_takeover
## listener.ws.external.deflate_opts.client_context_takeover = takeover

## The deflate options for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.deflate_opts.server_max_window_bits
##
## Valid range is 8-15
## listener.ws.external.deflate_opts.server_max_window_bits = 15

## The deflate options for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.deflate_opts.client_max_window_bits
##
## Valid range is 8-15
## listener.ws.external.deflate_opts.client_max_window_bits = 15

## The idle timeout for external MQTT/WebSocket connections.
##
## See: listener.ws.$name.idle_timeout
##
## Value: Duration
## listener.ws.external.idle_timeout = 60s

## The max frame size for external MQTT/WebSocket connections.
##
##
## Value: Number
## listener.ws.external.max_frame_size = 0

## Whether a WebSocket message is allowed to contain multiple MQTT packets
##
## Value: single | multiple
listener.ws.external.mqtt_piggyback = multiple

## By default, EMQX web socket connection does not restrict connections to specific origins.
## It also, by default, does not enforce the presence of origin in request headers for WebSocket connections.
## Because of this, a malicious user could potentially hijack an existing web-socket connection to EMQX.

## To prevent this, users can set allowed origin headers in their ws connection to EMQX.
## WS configs are set in listener.ws.external.*
## WSS configs are set in listener.wss.external.*

## Example for WS connection
## To enables origin check in header for websocket connnection,
## set `listener.ws.external.check_origin_enable = true`. By default it is false,
## When it is set to true and no origin is present in the header of a ws connection request, the request fails.

## To allow origins to be absent in header in the websocket connection when check_origin_enable is true,
## set `listener.ws.external.allow_origin_absence = true`

## Enabling origin check implies there are specific valid origins allowed for ws connection.
## To set the list of allowed origins in header for websocket connection
## listener.ws.external.check_origins = http://localhost:13020(localhost dashboard url), http://yourapp.com`
## check_origins config allows a comma separated list of origins so you can specify as many origins are you want.
## With these configs, you can allow only connections from only authorized origins to your broker

## Enable origin check in header for websocket connection
##
## Value: true | false (default false)
listener.ws.external.check_origin_enable = false

## Allow origin to be absent in header in websocket connection when check_origin_enable is true
##
## Value: true | false (default true)
listener.ws.external.allow_origin_absence = true

## Comma separated list of allowed origin in header for websocket connection
##
## Value: http://url eg. local http dashboard url - http://localhost:13020, http://127.0.0.1:13020
listener.ws.external.check_origins = http://localhost:13020, http://127.0.0.1:13020

##--------------------------------------------------------------------
## External WebSocket/SSL listener for MQTT Protocol

## listener.wss.$name is the IP address and port that the MQTT/WebSocket/SSL
## listener will bind.
##
## Value: IP:Port | Port
##
## Examples: 3030, 127.0.0.1:3030, ::1:3030
listener.wss.external = 3030

## The path of WebSocket MQTT endpoint
##
## Value: URL Path
listener.wss.external.mqtt_path = /mqtt

## The acceptor pool for external MQTT/WebSocket/SSL listener.
##
## Value: Number
listener.wss.external.acceptors = 4

## Maximum number of concurrent MQTT/Webwocket/SSL connections.
##
## Value: Number
listener.wss.external.max_connections = 102400

## Maximum MQTT/WebSocket/SSL connections per second.
##
## See: listener.tcp.$name.max_conn_rate
##
## Value: Number
listener.wss.external.max_conn_rate = 1000

## Simulate the {active, N} option for the MQTT/WebSocket/SSL connections.
##
## Value: Number
listener.wss.external.active_n = 100

## Zone of the external MQTT/WebSocket/SSL listener belonged to.
##
## Value: String
listener.wss.external.zone = external

## The access control rules for the MQTT/WebSocket/SSL listener.
##
## See: listener.tcp.$name.access.<no>
##
## Value: ACL Rule
listener.wss.external.access.1 = allow all

## If set to true, the server fails if the client does not have a Sec-WebSocket-Protocol to send.
## Set to false for WeChat MiniApp.
##
## Value: true | false
## listener.wss.external.fail_if_no_subprotocol = true

## Supported subprotocols
##
## Default: mqtt, mqtt-v3, mqtt-v3.1.1, mqtt-v5
## listener.wss.external.supported_subprotocols = mqtt, mqtt-v3, mqtt-v3.1.1, mqtt-v5

## Specify which HTTP header for real source IP if the EMQ X cluster is
## deployed behind NGINX or HAProxy.
##
## Default: X-Forwarded-For
## listener.wss.external.proxy_address_header = X-Forwarded-For

## Specify which HTTP header for real source port if the EMQ X cluster is
## deployed behind NGINX or HAProxy.
##
## Default: X-Forwarded-Port
## listener.wss.external.proxy_port_header = X-Forwarded-Port

## Enable the Proxy Protocol V1/2 support.
##
## See: listener.tcp.$name.proxy_protocol
##
## Value: on | off
## listener.wss.external.proxy_protocol = on

## Sets the timeout for proxy protocol.
##
## See: listener.tcp.$name.proxy_protocol_timeout
##
## Value: Duration
## listener.wss.external.proxy_protocol_timeout = 3s

## TLS versions only to protect from POODLE attack.
##
## See: listener.ssl.$name.tls_versions
##
## Value: String, seperated by ','
## NOTE: Do not use tlsv1.3 if emqx is running on OTP-22 or earlier
## listener.wss.external.tls_versions = tlsv1.3,tlsv1.2,tlsv1.1,tlsv1

## Path to the file containing the user's private PEM-encoded key.
##
## See: listener.ssl.$name.keyfile
##
## Value: File
listener.wss.external.keyfile = etc/certs/key.pem

## Path to a file containing the user certificate.
##
## See: listener.ssl.$name.certfile
##
## Value: File
listener.wss.external.certfile = etc/certs/cert.pem

## Path to the file containing PEM-encoded CA certificates.
##
## See: listener.ssl.$name.cacert
##
## Value: File
## listener.wss.external.cacertfile = etc/certs/cacert.pem

## Maximum number of non-self-issued intermediate certificates that
## can follow the peer certificate in a valid certification path.
##
## See: listener.ssl.external.depth
##
## Value: Number
## listener.wss.external.depth = 10

## String containing the user's password. Only used if the private keyfile
## is password-protected.
##
## See: listener.ssl.$name.key_password
##
## Value: String
## listener.wss.external.key_password = yourpass

## See: listener.ssl.$name.dhfile
##
## Value: File
## listener.ssl.external.dhfile = etc/certs/dh-params.pem

## See: listener.ssl.$name.verify
##
## Value: verify_peer | verify_none
## listener.wss.external.verify = verify_peer

## See: listener.ssl.$name.fail_if_no_peer_cert
##
## Value: false | true
## listener.wss.external.fail_if_no_peer_cert = true

## See: listener.ssl.$name.ciphers
##
## Value: Ciphers
listener.wss.external.ciphers = TLS_AES_256_GCM_SHA384,TLS_AES_128_GCM_SHA256,TLS_CHACHA20_POLY1305_SHA256,TLS_AES_128_CCM_SHA256,TLS_AES_128_CCM_8_SHA256,ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-ECDSA-AES256-SHA384,ECDHE-RSA-AES256-SHA384,ECDHE-ECDSA-DES-CBC3-SHA,ECDH-ECDSA-AES256-GCM-SHA384,ECDH-RSA-AES256-GCM-SHA384,ECDH-ECDSA-AES256-SHA384,ECDH-RSA-AES256-SHA384,DHE-DSS-AES256-GCM-SHA384,DHE-DSS-AES256-SHA256,AES256-GCM-SHA384,AES256-SHA256,ECDHE-ECDSA-AES128-GCM-SHA256,ECDHE-RSA-AES128-GCM-SHA256,ECDHE-ECDSA-AES128-SHA256,ECDHE-RSA-AES128-SHA256,ECDH-ECDSA-AES128-GCM-SHA256,ECDH-RSA-AES128-GCM-SHA256,ECDH-ECDSA-AES128-SHA256,ECDH-RSA-AES128-SHA256,DHE-DSS-AES128-GCM-SHA256,DHE-DSS-AES128-SHA256,AES128-GCM-SHA256,AES128-SHA256,ECDHE-ECDSA-AES256-SHA,ECDHE-RSA-AES256-SHA,DHE-DSS-AES256-SHA,ECDH-ECDSA-AES256-SHA,ECDH-RSA-AES256-SHA,AES256-SHA,ECDHE-ECDSA-AES128-SHA,ECDHE-RSA-AES128-SHA,DHE-DSS-AES128-SHA,ECDH-ECDSA-AES128-SHA,ECDH-RSA-AES128-SHA,AES128-SHA

## Ciphers for TLS PSK.
## Note that 'listener.wss.external.ciphers' and 'listener.wss.external.psk_ciphers' cannot
## be configured at the same time.
## See 'https://tools.ietf.org/html/rfc4279#section-2'.
## listener.wss.external.psk_ciphers = PSK-AES128-CBC-SHA,PSK-AES256-CBC-SHA,PSK-3DES-EDE-CBC-SHA,PSK-RC4-SHA

## See: listener.ssl.$name.secure_renegotiate
##
## Value: on | off
## listener.wss.external.secure_renegotiate = off

## See: listener.ssl.$name.reuse_sessions
##
## Value: on | off
## listener.wss.external.reuse_sessions = on

## See: listener.ssl.$name.honor_cipher_order
##
## Value: on | off
## listener.wss.external.honor_cipher_order = on

## See: listener.ssl.$name.peer_cert_as_username
##
## Value: cn | dn | crt | pem | md5
## listener.wss.external.peer_cert_as_username = cn

## See: listener.ssl.$name.peer_cert_as_clientid
##
## Value: cn | dn | crt | pem | md5
## listener.wss.external.peer_cert_as_clientid = cn

## TCP backlog for the WebSocket/SSL connection.
##
## See: listener.tcp.$name.backlog
##
## Value: Number >= 0
listener.wss.external.backlog = 1024

## The TCP send timeout for the WebSocket/SSL connection.
##
## See: listener.tcp.$name.send_timeout
##
## Value: Duration
listener.wss.external.send_timeout = 15s

## Close the WebSocket/SSL connection if send timeout.
##
## See: listener.tcp.$name.send_timeout_close
##
## Value: on | off
listener.wss.external.send_timeout_close = on

## The TCP receive buffer(os kernel) for the WebSocket/SSL connections.
##
## See: listener.tcp.$name.recbuf
##
## Value: Bytes
## listener.wss.external.recbuf = 4KB

## The TCP send buffer(os kernel) for the WebSocket/SSL connections.
##
## See: listener.tcp.$name.sndbuf
##
## Value: Bytes
## listener.wss.external.sndbuf = 4KB

## The size of the user-level software buffer used by the driver.
##
## See: listener.tcp.$name.buffer
##
## Value: Bytes
## listener.wss.external.buffer = 4KB

## The TCP_NODELAY flag for WebSocket/SSL connections.
##
## See: listener.tcp.$name.nodelay
##
## Value: true | false
## listener.wss.external.nodelay = true

## The compress flag for external WebSocket/SSL connections.
##
## If this Value is set true,the websocket message would be compressed
##
## Value: true | false
## listener.wss.external.compress = true

## The level of deflate options for external WebSocket/SSL connections.
##
## See: listener.wss.$name.deflate_opts.level
##
## Value: none | default | best_compression | best_speed
## listener.wss.external.deflate_opts.level = default

## The mem_level of deflate options for external WebSocket/SSL connections.
##
## See: listener.wss.$name.deflate_opts.mem_level
##
## Valid range is 1-9
## listener.wss.external.deflate_opts.mem_level = 8

## The strategy of deflate options for external WebSocket/SSL connections.
##
## See: listener.wss.$name.deflate_opts.strategy
##
## Value: default | filtered | huffman_only | rle
## listener.wss.external.deflate_opts.strategy = default

## The deflate option for external WebSocket/SSL connections.
##
## See: listener.wss.$name.deflate_opts.server_context_takeover
##
## Value: takeover | no_takeover
## listener.wss.external.deflate_opts.server_context_takeover = takeover

## The deflate option for external WebSocket/SSL connections.
##
## See: listener.wss.$name.deflate_opts.client_context_takeover
##
## Value: takeover | no_takeover
## listener.wss.external.deflate_opts.client_context_takeover = takeover

## The deflate options for external WebSocket/SSL connections.
##
## See: listener.wss.$name.deflate_opts.server_max_window_bits
##
## Valid range is 8-15
## listener.wss.external.deflate_opts.server_max_window_bits = 15

## The deflate options for external WebSocket/SSL connections.
##
## See: listener.wss.$name.deflate_opts.client_max_window_bits
##
## Valid range is 8-15
## listener.wss.external.deflate_opts.client_max_window_bits = 15

## The idle timeout for external WebSocket/SSL connections.
##
## See: listener.wss.$name.idle_timeout
##
## Value: Duration
## listener.wss.external.idle_timeout = 60s

## The max frame size for external WebSocket/SSL connections.
##
## Value: Number
## listener.wss.external.max_frame_size = 0

## Whether a WebSocket message is allowed to contain multiple MQTT packets
##
## Value: single | multiple
listener.wss.external.mqtt_piggyback = multiple
## Enable origin check in header for secure websocket connection
##
## Value: true | false (default false)
listener.wss.external.check_origin_enable = false
## Allow origin to be absent in header in secure websocket connection  when check_origin_enable is true
##
## Value: true | false (default true)
listener.wss.external.allow_origin_absence = true
## Comma separated list of allowed origin in header for secure websocket connection
##
## Value: http://url eg. https://localhost:3030, https://127.0.0.1:3030
listener.wss.external.check_origins = https://localhost:3030, https://127.0.0.1:3030

## CONFIG_SECTION_END=listeners ================================================

## CONFIG_SECTION_BGN=modules ==================================================

## The file to store loaded module names.
##
## Value: File
modules.loaded_file = data/loaded_modules

##--------------------------------------------------------------------
## Presence Module

## Sets the QoS for presence MQTT message.
##
## Value: 0 | 1 | 2
module.presence.qos = 1

##--------------------------------------------------------------------
## Subscription Module

## Subscribe the Topics automatically when client connected.
##
## Value: String
## module.subscription.1.topic = connected/%c/%u

## Qos of the proxy subscription.
##
## Value: 0 | 1 | 2
## Default: 0
## module.subscription.1.qos = 0

## No Local of the proxy subscription options.
## This configuration only takes effect in the MQTT V5 protocol.
##
## Value: 0 | 1
## Default: 0
## module.subscription.1.nl = 0

## Retain As Published of the proxy subscription options.
## This configuration only takes effect in the MQTT V5 protocol.
##
## Value: 0 | 1
## Default: 0
## module.subscription.1.rap = 0

## Retain Handling of the proxy subscription options.
## This configuration only takes effect in the MQTT V5 protocol.
##
## Value: 0 | 1 | 2
## Default: 0
## module.subscription.1.rh = 0

##--------------------------------------------------------------------
## Rewrite Module

## {rewrite, Topic, Re, Dest}
## module.rewrite.pub.rule.1 = x/# ^x/y/(.+)$ z/y/$1
## module.rewrite.sub.rule.1 = y/+/z/# ^y/(.+)/z/(.+)$ y/z/$2

## CONFIG_SECTION_END=modules ==================================================

##-------------------------------------------------------------------
## Plugins
##-------------------------------------------------------------------

## The etc dir for plugins' config.
##
## Value: Folder
plugins.etc_dir = etc/plugins/

## The file to store loaded plugin names.
##
## Value: File
plugins.loaded_file = data/loaded_plugins

## The directory of extension plugins.
##
## Value: File
plugins.expand_plugins_dir = etc/plugins/

##--------------------------------------------------------------------
## Broker
##--------------------------------------------------------------------

## System interval of publishing $SYS messages.
##
## Value: Duration
## Default: 1m, 1 minute
broker.sys_interval = 1m

## System heartbeat interval of publishing following heart beat message:
##  - "$SYS/brokers/<node>/uptime"
##  - "$SYS/brokers/<node>/datetime"
##
## Value: Duration
## Default: 30s
broker.sys_heartbeat = 30s

## Session locking strategy in a cluster.
##
## Value: Enum
## - local
## - leader
## - quorum
## - all
broker.session_locking_strategy = quorum

## Dispatch strategy for shared subscription
##
## Value: Enum
## - random
## - round_robin
## - sticky
## - hash # same as hash_clientid
## - hash_clientid
## - hash_topic
broker.shared_subscription_strategy = random

## Enable/disable shared dispatch acknowledgement for QoS1 and QoS2 messages
## This should allow messages to be dispatched to a different subscriber in
## the group in case the picked (based on shared_subscription_strategy) one # is offline
##
## Value: Enum
## - true
## - false
broker.shared_dispatch_ack_enabled = false

## Enable batch clean for deleted routes.
##
## Value: Flag
broker.route_batch_clean = off

## Performance toggle for subscribe/unsubscribe wildcard topic.
## Change this toggle only when there are many wildcard topics.
## Value: Enum
##  - key:   mnesia translational updates with per-key locks. recommended for single node setup.
##  - tab:   mnesia translational updates with table lock. recommended for multi-nodes setup.
##  - global: global lock protected updates. recommended for larger cluster.
## NOTE: when changing from/to 'global' lock, it requires all nodes in the cluster
## to be stopped before the change.
# broker.perf.route_lock_type = key

## Enable trie path compaction.
## Enabling it significantly improves wildcard topic subscribe rate,
## if wildcard topics have unique prefixes like: 'sensor//+/',
## where ID is unique per subscriber.
##
## Topic match performance (when publishing) may degrade if messages
## are mostly published to topics with large number of levels.
##
## NOTE: This is a cluster-wide configuration.
## It rquires all nodes to be stopped before changing it.
##
## Value: Enum
##  - true: enable trie path compaction
##  - false: disable trie path compaction
# broker.perf.trie_compaction = true

## CONFIG_SECTION_BGN=sys_mon ==================================================

## Enable Long GC monitoring. Disable if the value is 0.
## Notice: don't enable the monitor in production for:
## https://github.com/erlang/otp/blob/feb45017da36be78d4c5784d758ede619fa7bfd3/erts/emulator/beam/erl_gc.c#L421
##
## Value: Duration
##  - h: hour
##  - m: minute
##  - s: second
##  - ms: milliseconds
##
## Examples:
##  - 2h:  2 hours
##  - 30m: 30 minutes
##  - 0.1s: 0.1 seconds
##  - 100ms : 100 milliseconds
##
## Default: 0ms
sysmon.long_gc = 0

## Enable Long Schedule(ms) monitoring.
##
## See: http://erlang.org/doc/man/erlang.html#system_monitor-2
##
## Value: Duration
##  - h: hour
##  - m: minute
##  - s: second
##  - ms: milliseconds
##
## Examples:
##  - 2h:  2 hours
##  - 30m: 30 minutes
##  - 0.1s: 0.1 seconds
##  - 100ms: 100 milliseconds
##
## Default: 0ms
sysmon.long_schedule = 240ms

## Enable Large Heap monitoring.
##
## See: http://erlang.org/doc/man/erlang.html#system_monitor-2
##
## Value: bytes
##
## Default: 8M words. 32MB on 32-bit VM, 64MB on 64-bit VM.
sysmon.large_heap = 8MB

## Enable Busy Port monitoring.
##
## See: http://erlang.org/doc/man/erlang.html#system_monitor-2
##
## Value: true | false
sysmon.busy_port = false

## Enable Busy Dist Port monitoring.
##
## See: http://erlang.org/doc/man/erlang.html#system_monitor-2
##
## Value: true | false
sysmon.busy_dist_port = true

## The time interval for the periodic cpu check
##
## Value: Duration
## -h: hour, e.g. '2h' for 2 hours
## -m: minute, e.g. '5m' for 5 minutes
## -s: second, e.g. '30s' for 30 seconds
##
## Default: 60s
os_mon.cpu_check_interval = 60s

## The threshold, as percentage of system cpu, for how much system cpu can be used before the corresponding alarm is set.
##
## Default: 80%
os_mon.cpu_high_watermark = 80%

## The threshold, as percentage of system cpu, for how much system cpu can be used before the corresponding alarm is clear.
##
## Default: 60%
os_mon.cpu_low_watermark = 60%

## The time interval for the periodic memory check
##
## Value: Duration
## -h: hour, e.g. '2h' for 2 hours
## -m: minute, e.g. '5m' for 5 minutes
## -s: second, e.g. '30s' for 30 seconds
##
## Default: 60s
os_mon.mem_check_interval = 60s

## The threshold, as percentage of system memory, for how much system memory can be allocated before the corresponding alarm is set.
##
## Default: 70%
os_mon.sysmem_high_watermark = 70%

## The threshold, as percentage of system memory, for how much system memory can be allocated by one Erlang process before the corresponding alarm is set.
##
## Default: 5%
os_mon.procmem_high_watermark = 5%

## The time interval for the periodic process limit check
##
## Value: Duration
##
## Default: 30s
vm_mon.check_interval = 30s

## The threshold, as percentage of processes, for how many processes can simultaneously exist at the local node before the corresponding alarm is set.
##
## Default: 80%
vm_mon.process_high_watermark = 80%

## The threshold, as percentage of processes, for how many processes can simultaneously exist at the local node before the corresponding alarm is clear.
##
## Default: 60%
vm_mon.process_low_watermark = 60%

## Specifies the actions to take when an alarm is activated
##
## Value: String
##  - log
##  - publish
##
## Default: log,publish
alarm.actions = log,publish

## The maximum number of deactivated alarms
##
## Value: Integer
##
## Default: 1000
alarm.size_limit = 1000

## Validity Period of deactivated alarms
##
## Value: Duration
##  - h: hour
##  - m: minute
##  - s: second
##  - ms: milliseconds
##
## Default: 24h
alarm.validity_period = 24h

## CONFIG_SECTION_END=sys_mon ==================================================
```



##### 4.2.2 `plugins/emqx_dashboard.conf`

```yaml
##--------------------------------------------------------------------
## EMQ X Dashboard
##--------------------------------------------------------------------

## Default user's login name.
##
## Value: String
dashboard.default_user.login = admin

## Default user's password.
##
## Value: String
dashboard.default_user.password = public

##--------------------------------------------------------------------
## HTTP Listener

## The port that the Dashboard HTTP listener will bind.
##
## Value: Port
##
## Examples: 18083
dashboard.listener.http = 3050

## The acceptor pool for external Dashboard HTTP listener.
##
## Value: Number
dashboard.listener.http.acceptors = 4

## Maximum number of concurrent Dashboard HTTP connections.
##
## Value: Number
dashboard.listener.http.max_clients = 512

## Set up the socket for IPv6.
##
## Value: false | true
dashboard.listener.http.inet6 = false

## Listen on IPv4 and IPv6 (false) or only on IPv6 (true). Use with inet6.
##
## Value: false | true
dashboard.listener.http.ipv6_v6only = false

##--------------------------------------------------------------------
## HTTPS Listener

## The port that the Dashboard HTTPS listener will bind.
##
## Value: Port
##
## Examples: 18084
## dashboard.listener.https = 18084

## The acceptor pool for external Dashboard HTTPS listener.
##
## Value: Number
## dashboard.listener.https.acceptors = 2

## Maximum number of concurrent Dashboard HTTPS connections.
##
## Value: Number
## dashboard.listener.https.max_clients = 512

## Set up the socket for IPv6.
##
## Value: false | true
## dashboard.listener.https.inet6 = false

## Listen on IPv4 and IPv6 (false) or only on IPv6 (true). Use with inet6.
##
## Value: false | true
## dashboard.listener.https.ipv6_v6only = false

## Path to the file containing the user's private PEM-encoded key.
##
## Value: File
## dashboard.listener.https.keyfile = etc/certs/key.pem

## Path to a file containing the user certificate.
##
## Value: File
## dashboard.listener.https.certfile = etc/certs/cert.pem

## Path to the file containing PEM-encoded CA certificates.
##
## Value: File
## dashboard.listener.https.cacertfile = etc/certs/cacert.pem

## See: 'listener.ssl.<name>.dhfile' in emq.conf
##
## Value: File
## dashboard.listener.https.dhfile = etc/certs/dh-params.pem

## See: 'listener.ssl.<name>.verify' in emq.conf
##
## Value: verify_peer | verify_none
## dashboard.listener.https.verify = verify_peer

## See: 'listener.ssl.<name>.fail_if_no_peer_cert' in emq.conf
##
## Value: false | true
## dashboard.listener.https.fail_if_no_peer_cert = true

## TLS versions only to protect from POODLE attack.
##
## Value: String, seperated by ','
## NOTE: Do not use tlsv1.3 if emqx is running on OTP-22 or earlier
## dashboard.listener.https.tls_versions = tlsv1.3,tlsv1.2,tlsv1.1,tlsv1

## See: 'listener.ssl.<name>.ciphers' in emq.conf
##
## Value: Ciphers
## dashboard.listener.https.ciphers = TLS_AES_256_GCM_SHA384,TLS_AES_128_GCM_SHA256,TLS_CHACHA20_POLY1305_SHA256,TLS_AES_128_CCM_SHA256,TLS_AES_128_CCM_8_SHA256,ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-ECDSA-AES256-SHA384,ECDHE-RSA-AES256-SHA384,ECDHE-ECDSA-DES-CBC3-SHA,ECDH-ECDSA-AES256-GCM-SHA384,ECDH-RSA-AES256-GCM-SHA384,ECDH-ECDSA-AES256-SHA384,ECDH-RSA-AES256-SHA384,DHE-DSS-AES256-GCM-SHA384,DHE-DSS-AES256-SHA256,AES256-GCM-SHA384,AES256-SHA256,ECDHE-ECDSA-AES128-GCM-SHA256,ECDHE-RSA-AES128-GCM-SHA256,ECDHE-ECDSA-AES128-SHA256,ECDHE-RSA-AES128-SHA256,ECDH-ECDSA-AES128-GCM-SHA256,ECDH-RSA-AES128-GCM-SHA256,ECDH-ECDSA-AES128-SHA256,ECDH-RSA-AES128-SHA256,DHE-DSS-AES128-GCM-SHA256,DHE-DSS-AES128-SHA256,AES128-GCM-SHA256,AES128-SHA256,ECDHE-ECDSA-AES256-SHA,ECDHE-RSA-AES256-SHA,DHE-DSS-AES256-SHA,ECDH-ECDSA-AES256-SHA,ECDH-RSA-AES256-SHA,AES256-SHA,ECDHE-ECDSA-AES128-SHA,ECDHE-RSA-AES128-SHA,DHE-DSS-AES128-SHA,ECDH-ECDSA-AES128-SHA,ECDH-RSA-AES128-SHA,AES128-SHA

## See: 'listener.ssl.<name>.secure_renegotiate' in emq.conf
##
## Value: on | off
## dashboard.listener.https.secure_renegotiate = off

## See: 'listener.ssl.<name>.reuse_sessions' in emq.conf
##
## Value: on | off
## dashboard.listener.https.reuse_sessions = on

## See: 'listener.ssl.<name>.honor_cipher_order' in emq.conf
##
## Value: on | off
## dashboard.listener.https.honor_cipher_order = on
```



##### 4.2.3 `plugins/emqx_management.conf`

```yaml
##--------------------------------------------------------------------
## EMQ X Management Plugin
##--------------------------------------------------------------------

## Max Row Limit
management.max_row_limit = 10000

## Application default secret
##
## Value: String
## management.application.default_secret = public

## Default Application ID
##
## Value: String
management.default_application.id = admin

## Default Application Secret
##
## Value: String
management.default_application.secret = public

##--------------------------------------------------------------------
## HTTP Listener

management.listener.http = 3040
management.listener.http.acceptors = 2
management.listener.http.max_clients = 512
management.listener.http.backlog = 512
management.listener.http.send_timeout = 15s
management.listener.http.send_timeout_close = on
management.listener.http.inet6 = false
management.listener.http.ipv6_v6only = false

##--------------------------------------------------------------------
## HTTPS Listener

## management.listener.https = 8081
## management.listener.https.acceptors = 2
## management.listener.https.max_clients = 512
## management.listener.https.backlog = 512
## management.listener.https.send_timeout = 15s
## management.listener.https.send_timeout_close = on
## management.listener.https.certfile = etc/certs/cert.pem
## management.listener.https.keyfile = etc/certs/key.pem
## management.listener.https.cacertfile = etc/certs/cacert.pem
## management.listener.https.verify = verify_peer
## NOTE: Do not use tlsv1.3 if emqx is running on OTP-22 or earlier
## management.listener.https.tls_versions = tlsv1.3,tlsv1.2,tlsv1.1,tlsv1
## management.listener.https.ciphers = TLS_AES_256_GCM_SHA384,TLS_AES_128_GCM_SHA256,TLS_CHACHA20_POLY1305_SHA256,TLS_AES_128_CCM_SHA256,TLS_AES_128_CCM_8_SHA256,ECDHE-ECDSA-AES256-GCM-SHA384,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-ECDSA-AES256-SHA384,ECDHE-RSA-AES256-SHA384,ECDHE-ECDSA-DES-CBC3-SHA,ECDH-ECDSA-AES256-GCM-SHA384,ECDH-RSA-AES256-GCM-SHA384,ECDH-ECDSA-AES256-SHA384,ECDH-RSA-AES256-SHA384,DHE-DSS-AES256-GCM-SHA384,DHE-DSS-AES256-SHA256,AES256-GCM-SHA384,AES256-SHA256,ECDHE-ECDSA-AES128-GCM-SHA256,ECDHE-RSA-AES128-GCM-SHA256,ECDHE-ECDSA-AES128-SHA256,ECDHE-RSA-AES128-SHA256,ECDH-ECDSA-AES128-GCM-SHA256,ECDH-RSA-AES128-GCM-SHA256,ECDH-ECDSA-AES128-SHA256,ECDH-RSA-AES128-SHA256,DHE-DSS-AES128-GCM-SHA256,DHE-DSS-AES128-SHA256,AES128-GCM-SHA256,AES128-SHA256,ECDHE-ECDSA-AES256-SHA,ECDHE-RSA-AES256-SHA,DHE-DSS-AES256-SHA,ECDH-ECDSA-AES256-SHA,ECDH-RSA-AES256-SHA,AES256-SHA,ECDHE-ECDSA-AES128-SHA,ECDHE-RSA-AES128-SHA,DHE-DSS-AES128-SHA,ECDH-ECDSA-AES128-SHA,ECDH-RSA-AES128-SHA,AES128-SHA
## management.listener.https.fail_if_no_peer_cert = true
## management.listener.https.inet6 = false
## management.listener.https.ipv6_v6only = false
```



#### 4.3 启动

```bash
/data/emqx/dc3/node-01/bin/emqx start

/data/emqx/dc3/node-02/bin/emqx start

/data/emqx/dc3/node-03/bin/emqx start
```



#### 4.4 节点配置

> **配置 `节点2` 加入 `节点1`**

```bash
/data/emqx/dc3/node-02/bin/emqx_ctl cluster join node-01@127.0.0.1
```



> **配置 `节点3` 加入 `节点1`**

```bash
/data/emqx/dc3/node-03/bin/emqx_ctl cluster join node-01@127.0.0.1
```



> **节点状态**

```bash
/data/emqx/dc3/node-03/bin/emqx_ctl cluster status
```
