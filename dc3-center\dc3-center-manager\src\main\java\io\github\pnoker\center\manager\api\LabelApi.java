/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.github.pnoker.center.manager.api;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.github.pnoker.api.center.manager.feign.LabelClient;
import io.github.pnoker.center.manager.service.LabelService;
import io.github.pnoker.common.bean.R;
import io.github.pnoker.common.constant.ServiceConstant;
import io.github.pnoker.common.dto.GroupDto;
import io.github.pnoker.common.dto.LabelDto;
import io.github.pnoker.common.model.Label;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 标签 Client 接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(ServiceConstant.Manager.LABEL_URL_PREFIX)
public class LabelApi implements LabelClient {

    @Resource
    private LabelService labelService;

    @Override
    public R<Label> add(Label label, String tenantId) {
        try {
            Label add = labelService.add(label.setTenantId(tenantId));
            if (ObjectUtil.isNotNull(add)) {
                return R.ok(add);
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

    @Override
    public R<Boolean> delete(String id) {
        try {
            return labelService.delete(id) ? R.ok() : R.fail();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @Override
    public R<Label> update(Label label, String tenantId) {
        try {
            Label update = labelService.update(label.setTenantId(tenantId));
            if (ObjectUtil.isNotNull(update)) {
                return R.ok(update);
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

    @Override
    public R<Label> selectById(String id) {
        try {
            Label select = labelService.selectById(id);
            if (ObjectUtil.isNotNull(select)) {
                return R.ok(select);
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return R.fail();
    }

    @Override
    public R<Page<Label>> list(LabelDto labelDto, String tenantId) {
        try {
            if (ObjectUtil.isEmpty(labelDto)) {
                labelDto = new LabelDto();
            }
            labelDto.setTenantId(tenantId);
            Page<Label> page = labelService.list(labelDto);
            if (ObjectUtil.isNotNull(page)) {
                return R.ok(page);
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return R.fail();
    }
}
