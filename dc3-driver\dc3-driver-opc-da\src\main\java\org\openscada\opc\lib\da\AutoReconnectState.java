/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.openscada.opc.lib.da;

/**
 * A state for the auto-reconnect controller
 *
 * <AUTHOR>
 */
public enum AutoReconnectState {
    /**
     * Auto reconnect is disabled.
     */
    DISABLED,
    /**
     * Auto reconnect is enabled, but the connection is currently not established.
     */
    DISCONNECTED,
    /**
     * Auto reconnect is enabled, the connection is not established and the controller
     * is currently waiting the delay until it will reconnect.
     */
    WAITING,
    /**
     * Auto reconnect is enabled, the connection is not established but the controller
     * currently tries to establish the connection.
     */
    CONNECTING,
    /**
     * Auto reconnect is enabled and the connection is established.
     */
    CONNECTED
}