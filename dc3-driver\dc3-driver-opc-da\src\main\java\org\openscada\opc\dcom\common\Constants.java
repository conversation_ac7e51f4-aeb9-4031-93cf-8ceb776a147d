/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.openscada.opc.dcom.common;

public interface Constants {
    public static final String IConnectionPointContainer_IID = "B196B284-BAB4-101A-B69C-00AA00341D07";

    public static final String IConnectionPoint_IID = "B196B286-BAB4-101A-B69C-00AA00341D07";

    public static final String IOPCCommon_IID = "F31DFDE2-07B6-11D2-B2D8-0060083BA1FB";

    public static final String IEnumString_IID = "00000101-0000-0000-C000-000000000046";

    public static final String IEnumGUID_IID = "0002E000-0000-0000-C000-000000000046";

    public static final int S_OK = 0;

    public static final int S_FALSE = 1;
}
