#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

version: '3'

services:
  dc3:
    build:
      context: ../
      dockerfile: ./Dockerfile
    image: pnoker/dc3:1.0.0

  gateway:
    build:
      context: ../dc3-gateway/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-gateway:1.0.0
    restart: always
    ports:
      - 8000:8000
    environment:
      - NODE_ENV=dev
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-gateway
    hostname: dc3-gateway
    volumes:
      - logs:/dc3-gateway/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-gateway

  register:
    build:
      context: ../dc3-center/dc3-center-register/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-center-register:1.0.0
    restart: always
    ports:
      - 8100:8100
    environment:
      - NODE_ENV=dev
      - EUREKA_DASHBOARD_PATH=/
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-center-register
    hostname: dc3-center-register
    volumes:
      - logs:/dc3-center/dc3-center-register/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-center-register

  monitor:
    build:
      context: ../dc3-center/dc3-center-monitor/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-center-monitor:1.0.0
    restart: always
    ports:
      - 8200:8200
    environment:
      - NODE_ENV=dev
      - MONITOR_CONTEXT_PATH=/
      - MONITOR_PUBLIC_URL=/
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-center-monitor
    hostname: dc3-center-monitor
    volumes:
      - logs:/dc3-center/dc3-center-monitor/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-center-monitor

  auth:
    build:
      context: ../dc3-center/dc3-center-auth/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-center-auth:1.0.0
    restart: always
    ports:
      - 8300:8300
    environment:
      - NODE_ENV=dev
      - MYSQL_HOST=dc3-mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=dc3
      - REDIS_HOST=dc3-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dc3
      - CACHE_REDIS_TIME_TO_LIVE=6H
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-center-auth
    hostname: dc3-center-auth
    volumes:
      - logs:/dc3-center/dc3-center-auth/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-center-auth

  manager:
    build:
      context: ../dc3-center/dc3-center-manager/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-center-manager:1.0.0
    restart: always
    ports:
      - 8400:8400
    environment:
      - NODE_ENV=dev
      - MYSQL_HOST=dc3-mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=dc3
      - REDIS_HOST=dc3-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dc3
      - MONGO_HOST=dc3-mongo
      - MONGO_PORT=27017
      - MONGO_DATABASE=dc3
      - MONGO_AUTH_TYPE=password
      - MONGO_USERNAME=dc3
      - MONGO_PASSWORD=dc3
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - CACHE_REDIS_TIME_TO_LIVE=6H
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-center-manager
    hostname: dc3-center-manager
    volumes:
      - logs:/dc3-center/dc3-center-manager/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-center-manager

  data:
    build:
      context: ../dc3-center/dc3-center-data/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-center-data:1.0.0
    restart: always
    ports:
      - 8500:8500
    environment:
      - NODE_ENV=dev
      - MYSQL_HOST=dc3-mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=dc3
      - REDIS_HOST=dc3-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dc3
      - MONGO_HOST=dc3-mongo
      - MONGO_PORT=27017
      - MONGO_DATABASE=dc3
      - MONGO_AUTH_TYPE=password
      - MONGO_USERNAME=dc3
      - MONGO_PASSWORD=dc3
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - CACHE_REDIS_TIME_TO_LIVE=6H
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-center-data
    hostname: dc3-center-data
    volumes:
      - logs:/dc3-center/dc3-center-data/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-center-data

  virtual:
    build:
      context: ../dc3-driver/dc3-driver-virtual/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-driver-virtual:1.0.0
    restart: always
    ports:
      - 8600:8600
    environment:
      - NODE_ENV=dev
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-driver-virtual
    hostname: dc3-driver-virtual
    volumes:
      - logs:/dc3-driver/dc3-driver-virtual/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-driver-virtual

  plcs7:
    build:
      context: ../dc3-driver/dc3-driver-plcs7/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-driver-plcs7:1.0.0
    restart: always
    ports:
      - 8601:8601
    environment:
      - NODE_ENV=dev
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-driver-plcs7
    hostname: dc3-driver-plcs7
    volumes:
      - logs:/dc3-driver/dc3-driver-plcs7/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-driver-plcs7

  opc-da:
    build:
      context: ../dc3-driver/dc3-driver-opc-da/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-driver-opc-da:1.0.0
    restart: always
    ports:
      - 8602:8602
    environment:
      - NODE_ENV=dev
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-driver-opc-da
    hostname: dc3-driver-opc-da
    volumes:
      - logs:/dc3-driver/dc3-driver-opc-da/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-driver-opc-da

  opc-ua:
    build:
      context: ../dc3-driver/dc3-driver-opc-ua/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-driver-opc-ua:1.0.0
    restart: always
    ports:
      - 8603:8603
    environment:
      - NODE_ENV=dev
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-driver-opc-ua
    hostname: dc3-driver-opc-ua
    volumes:
      - logs:/dc3-driver/dc3-driver-opc-ua/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-driver-opc-ua

  listening-virtual:
    build:
      context: ../dc3-driver/dc3-driver-listening-virtual/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-driver-listening-virtual:1.0.0
    restart: always
    ports:
      - 8700:8700
    environment:
      - NODE_ENV=dev
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-driver-listening-virtual
    hostname: dc3-driver-listening-virtual
    volumes:
      - logs:/dc3-driver/dc3-driver-listening-virtual/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-driver-listening-virtual

  mqtt:
    build:
      context: ../dc3-driver/dc3-driver-mqtt/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-driver-mqtt:1.0.0
    restart: always
    ports:
      - 8701:8701
    environment:
      - NODE_ENV=dev
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-driver-mqtt
    hostname: dc3-driver-mqtt
    volumes:
      - logs:/dc3-driver/dc3-driver-mqtt/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-driver-mqtt

  modbus-tcp:
    build:
      context: ../dc3-driver/dc3-driver-modbus-tcp/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-driver-modbus-tcp:1.0.0
    restart: always
    ports:
      - 8604:8604
    environment:
      - NODE_ENV=dev
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-driver-modbus-tcp
    hostname: dc3-driver-modbus-tcp
    volumes:
      - logs:/dc3-driver/dc3-driver-modbus-tcp/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-driver-modbus-tcp

  edge-gateway:
    build:
      context: ../dc3-driver/dc3-driver-edge-gateway/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-driver-edge-gateway:1.0.0
    restart: always
    ports:
      - 8702:8702
    environment:
      - NODE_ENV=dev
      - RABBITMQ_VIRTUAL_HOST=dc3
      - RABBITMQ_HOST=dc3-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=dc3
      - RABBITMQ_PASSWORD=dc3
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-driver-edge-gateway
    hostname: dc3-driver-edge-gateway
    volumes:
      - logs:/dc3-driver/dc3-driver-edge-gateway/dc3/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-driver-edge-gateway

  rtmp:
    build:
      context: ../dc3-transfer/dc3-transfer-rtmp/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-transfer-rtmp:1.0.0
    restart: always
    ports:
      - 8803:8803
    environment:
      - NODE_ENV=dev
      - MYSQL_HOST=dc3-mysql
      - MYSQL_PORT=3306
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=dc3
      - REDIS_HOST=dc3-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=dc3
      - CACHE_REDIS_TIME_TO_LIVE=6H
      - EUREKA_HOST=dc3-center-register
      - EUREKA_PORT=8100
      - SECURITY_USER_NAME=dc3
      - SECURITY_USER_PASSWORD=dc3
    container_name: dc3-transfer-rtmp
    hostname: dc3-transfer-rtmp
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-transfer-rtmp

  mysql:
    build:
      context: ./dependencies/mysql/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-mysql:1.0.0
    restart: always
    ports:
      - 3306:3306
    environment:
      - MYSQL_ROOT_PASSWORD=dc3
    container_name: dc3-mysql
    hostname: dc3-mysql
    volumes:
      - mysql:/var/lib/mysql
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-mysql

  mongo:
    build:
      context: ./dependencies/mongo/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-mongo:1.0.0
    restart: always
    ports:
      - 27017:27017
    container_name: dc3-mongo
    hostname: dc3-mongo
    volumes:
      - mongo_config:/data/configdb
      - mongo_db:/data/db
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-mongo

  opentsdb:
    build:
      context: ./dependencies/opentsdb/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-opentsdb:1.0.0
    restart: always
    ports:
      - 4242:4242
    container_name: dc3-opentsdb
    hostname: dc3-opentsdb
    volumes:
      - opentsdb_data:/data/hbase
      - opentsdb_tmp:/tmp
      - opentsdb_plugins:/opentsdb-plugins
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-opentsdb

  influxdb:
    build:
      context: ./dependencies/influxdb/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-influxdb:1.0.0
    restart: always
    ports:
      - 8086:8086
    container_name: dc3-influxdb
    hostname: dc3-influxdb
    volumes:
      - influxdb:/var/lib/influxdb
      - influxdb2:/var/lib/influxdb2
      - influxdb2_etc:/etc/influxdb2
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-influxdb

  redis:
    build:
      context: ./dependencies/redis/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-redis:1.0.0
    restart: always
    ports:
      - 6379:6379
    container_name: dc3-redis
    hostname: dc3-redis
    volumes:
      - redis:/data
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-redis

  rabbitmq:
    build:
      context: ./dependencies/rabbitmq/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-rabbitmq:1.0.0
    restart: always
    ports:
      - 5672:5672
      - 1883:1883
      - 61613:61613
      - 15672:15672
    container_name: dc3-rabbitmq
    hostname: dc3-rabbitmq
    volumes:
      - rabbitmq:/var/lib/rabbitmq
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-rabbitmq

  emqx:
    build:
      context: ./dependencies/emqx/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-emqx:1.0.0
    restart: always
    ports:
      #- 1883:1883
      - 8883:8883
      - 18083:18083
    container_name: dc3-emqx
    hostname: dc3-emqx
    volumes:
      - emqx_etc:/opt/emqx/etc
      - emqx_lib:/opt/emqx/lib
      - emqx_data:/opt/emqx/data
      - emqx_log:/opt/emqx/log
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-emqx

  grafana:
    build:
      context: ./dependencies/grafana/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-grafana:1.0.0
    restart: always
    ports:
      - 3000:3000
    container_name: dc3-grafana
    hostname: dc3-grafana
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-grafana

  flink:
    image: flink:1.10
    command: taskmanager
    restart: always
    expose:
      - 6121
      - 6122
    environment:
      - JOB_MANAGER_RPC_ADDRESS=jobmanager
    container_name: dc3-flink-task
    depends_on:
      - jobmanager
    links:
      - jobmanager:jobmanager
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-flink-task

  jobmanager:
    image: flink:1.10
    command: jobmanager
    restart: always
    expose:
      - 6123
    ports:
      - 8081:8081
    environment:
      - JOB_MANAGER_RPC_ADDRESS=jobmanager
    container_name: dc3-flink-job
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-flink-job

  elasticsearch:
    build:
      context: ./dependencies/elasticsearch/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-elasticsearch:1.0.0
    restart: always
    ports:
      - 9200:9200
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx2048m
    container_name: dc3-elasticsearch
    hostname: dc3-elasticsearch
    #volumes:
    #  - es_data:/usr/share/elasticsearch/data
    #  - es_logs:/usr/share/elasticsearch/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-elasticsearch

  kibana:
    build:
      context: ./dependencies/kibana/
      dockerfile: ./Dockerfile
    image: pnoker/dc3-kibana:1.0.0
    restart: always
    ports:
      - 5601:5601
    container_name: dc3-kibana
    hostname: dc3-kibana
    #volumes:
    #  - kibana_data:/usr/share/kibana/data
    #  - kibana_logs:/usr/share/kibana/logs
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-kibana

  nginx-rtmp:
    image: pnoker/alpine-nginx:rtmp
    restart: always
    ports:
      - 1935:1935
    container_name: dc3-nginx
    hostname: dc3-nginx
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-nginx

  portainer:
    image: portainer/portainer:alpine
    command: -H unix:///var/run/docker.sock
    restart: always
    ports:
      - 9000:9000
    container_name: portainer
    hostname: dc3-portainer
    volumes:
      - portainer:/data
      - /var/run/docker.sock:/var/run/docker.sock
    logging:
      driver: json-file
      options:
        max-size: '20m'
        max-file: '20'
    networks:
      dc3net:
        aliases:
          - dc3-portainer

volumes:
  logs:
  mysql:
  mongo_config:
  mongo_db:
  redis:
  rabbitmq:
  emqx_etc:
  emqx_lib:
  emqx_data:
  emqx_log:
  opentsdb_data:
  opentsdb_tmp:
  opentsdb_plugins:
  influxdb:
  influxdb2:
  influxdb2_etc:
  es_data:
  es_logs:
  kibana_data:
  kibana_logs:
  portainer:

networks:
  dc3net:
    driver: 'bridge'
...
