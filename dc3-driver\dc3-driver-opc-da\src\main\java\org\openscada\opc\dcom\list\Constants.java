/*
 * Copyright 2022 Pnoker All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.openscada.opc.dcom.list;

public interface Constants extends org.openscada.opc.dcom.common.Constants {
    public static final String IOPCServerList_IID = "13486D50-4821-11D2-A494-3CB306C10000";

    public static final String OPCServerList_CLSID = "13486D51-4821-11D2-A494-3CB306C10000";
}
