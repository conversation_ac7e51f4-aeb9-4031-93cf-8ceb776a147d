{"info": {"_postman_id": "e8b40186-1a1d-4965-be86-67b818c058dd", "name": "dc3-center-auth", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Token", "item": [{"name": "1.生成 SALT", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8300/auth/token/salt?username=pnoker", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "token", "salt"], "query": [{"key": "username", "value": "pnoker", "description": "用户名，不为空"}]}}, "response": []}, {"name": "2.生成 TOKEN", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"pnoker\",\n    \"password\": \"c99fa5218866e5443c7b23ea7ffe62af\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8300/auth/token/generate", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "token", "generate"]}, "description": "**说明：**\n\n - 1.用户名/密码：pnoker/10e339be1130a90dc1b9ff0332abced6\n\n - 2.生成MD5加密之后的密码：Dc3Util.md5(\"10e339be1130a90dc1b9ff0332abced6\"+\"salt\")，其中salt是第一步[1.获取SALT]中获取的\n\n---\n\n**例如：**\n\n - 1.salt是：fwl6rojy63jawkfu\n\n - 2.MD5加密之后的密码是：\"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJwbm9rZXIiLCJpc3MiOiJwbm9rZXIvZGMzLy0xIiwiaWF0IjoxNjIxODQ4MzE3LCJleHAiOjE2MjE4OTE1MTd9.ZfntzrNUtSjTiN81tODM61-2yJWhmiB8scTdD_1nT3U\""}, "response": []}, {"name": "3.校验 TOKEN 是否有效", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8300/auth/token/check?username=pnoker&salt=yf08g6on1rkdksdy&token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJwbm9rZXIiLCJpc3MiOiJwbm9rZXIvZGMzIiwiaWF0IjoxNjIxODUwMjMxLCJleHAiOjE2MjE4OTM0MzF9.utViU2yM5YpOblpmoh6fh996bdmEfFKg7p55xltp_go", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "token", "check"], "query": [{"key": "username", "value": "pnoker", "description": "用户名，不为空"}, {"key": "salt", "value": "yf08g6on1rkdksdy", "description": "salt"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJwbm9rZXIiLCJpc3MiOiJwbm9rZXIvZGMzIiwiaWF0IjoxNjIxODUwMjMxLCJleHAiOjE2MjE4OTM0MzF9.utViU2yM5YpOblpmoh6fh996bdmEfFKg7p55xltp_go", "description": "token"}]}}, "response": []}, {"name": "4.注销 TOKE", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8300/auth/token/cancel?username=pnoker", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "token", "cancel"], "query": [{"key": "username", "value": "pnoker", "description": "用户名，不为空"}]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "BlackIp", "item": [{"name": "1.新增 BlackIp", "request": {"method": "POST", "header": [{"key": "X-Auth-Tenant-Id", "value": "-1", "description": "DC3 租户 ID", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ip\": \"*********\",\n  \"description\": \"测试黑名单Ip\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8300/auth/black_ip/add", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "black_ip", "add"]}}, "response": []}, {"name": "2.修改 BlackIp", "request": {"method": "POST", "header": [{"key": "X-Auth-Tenant-Id", "value": "-1", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1396793287667081217,\n  \"ip\": \"*********\",\n  \"enable\": false,\n  \"description\": \"测试黑名单Ip\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8300/auth/black_ip/update", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "black_ip", "update"]}}, "response": []}, {"name": "3.根据 ID 查询 BlackIp", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8300/auth/black_ip/id/1396793287667081217", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "black_ip", "id", "1396793287667081217"]}}, "response": []}, {"name": "4.根据 Ip 查询 BlackIp", "request": {"method": "GET", "header": [{"key": "X-Auth-Tenant-Id", "value": "-1", "type": "text"}], "url": {"raw": "http://localhost:8300/auth/black_ip/ip/*********", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "black_ip", "ip", "*********"]}}, "response": []}, {"name": "5.分页查询 BlackIp", "request": {"method": "POST", "header": [{"key": "X-Auth-Tenant-Id", "value": "-1", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ip\": \"\",\n  \"page\": {\n    \"current\": 1,\n    \"size\": 500,\n    \"orders\": [\n      {\n        \"column\": \"id\",\n        \"asc\": false\n      }\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8300/auth/black_ip/list", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "black_ip", "list"]}}, "response": []}, {"name": "6.查询 BlackIp 是否存在并有效", "request": {"method": "GET", "header": [{"key": "X-Auth-Tenant-Id", "value": "-1", "type": "text"}], "url": {"raw": "http://localhost:8300/auth/black_ip/check/*********", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "black_ip", "check", "*********"]}}, "response": []}, {"name": "7.根据 ID 删除 BlackIp", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8300/auth/black_ip/delete/1396793287667081217", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "black_ip", "delete", "1396793287667081217"]}}, "response": []}], "protocolProfileBehavior": {}}, {"name": "User", "item": [{"name": "1.新增 User", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"dc3\",\n  \"password\": \"dc3dc3dc3\",\n  \"description\": \"平台开发者账号\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8300/auth/user/add", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "user", "add"]}}, "response": []}, {"name": "2.修改 User", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"password\": \"dc3dc3dc3\",\n  \"enable\": true,\n  \"description\": \"平台开发者账号\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8300/auth/user/update", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "user", "update"]}}, "response": []}, {"name": "3.根据 ID 重置 User 密码", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8300/auth/user/reset/1397392734298013698", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "user", "reset", "1397392734298013698"]}}, "response": []}, {"name": "4.根据 ID 查询 User", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8300/auth/user/id/1397392734298013698", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "user", "id", "1397392734298013698"]}}, "response": []}, {"name": "5.根据 Name 查询 User", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8300/auth/user/name/dc3", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "user", "name", "dc3"]}}, "response": []}, {"name": "6.分页查询 User", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"\",\n  \"page\": {\n    \"current\": 1,\n    \"size\": 500,\n    \"orders\": [\n      {\n        \"column\": \"id\",\n        \"asc\": false\n      }\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8300/auth/user/list", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "user", "list"]}}, "response": []}, {"name": "7.检测 User 是否存在并有效", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8300/auth/user/check/dc3", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "user", "check", "dc3"]}}, "response": []}, {"name": "8.根据 ID 删除 User", "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8300/auth/user/delete/1397392734298013698", "protocol": "http", "host": ["localhost"], "port": "8300", "path": ["auth", "user", "delete", "1397392734298013698"]}}, "response": []}], "protocolProfileBehavior": {}}], "protocolProfileBehavior": {}}