<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2022 Pnoker All Rights Reserved
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.github.pnoker</groupId>
        <artifactId>iot-dc3</artifactId>
        <version>2022.1.0</version>
    </parent>

    <artifactId>dc3-driver</artifactId>
    <packaging>pom</packaging>

    <description>IOT DC3 平台 设备协议驱动集合。</description>

    <modules>
        <module>dc3-driver-virtual</module>
        <module>dc3-driver-listening-virtual</module>
        <module>dc3-driver-plcs7</module>
        <module>dc3-driver-opc-da</module>
        <module>dc3-driver-opc-ua</module>
        <module>dc3-driver-mqtt</module>
        <module>dc3-driver-modbus-tcp</module>
        <module>dc3-driver-edge-gateway</module>
    </modules>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!-- Dc3 Driver Sdk -->
        <dependency>
            <groupId>io.github.pnoker</groupId>
            <artifactId>dc3-common-sdk</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
