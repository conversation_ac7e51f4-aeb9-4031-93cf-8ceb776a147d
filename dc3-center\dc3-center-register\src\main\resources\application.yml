#
# Copyright (c) 2022. Pnoker. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     http://www.apache.org/licenses/LICENSE-2.0
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

server:
  port: ${EUREKA_PORT:8100}

spring:
  application:
    name: @project.artifactId@
  profiles:
    active:
      - server
      - register
      - monitor
      - ${NODE_ENV:dev}

logging:
  level:
    io.github.pnoker: DEBUG
  file:
    name: dc3/logs/center/register/${spring.application.name}.log

eureka:
  dashboard:
    path: ${EUREKA_DASHBOARD_PATH:/}
  server:
    eviction-interval-timer-in-ms: 5000
    renewal-percent-threshold: 0.9
