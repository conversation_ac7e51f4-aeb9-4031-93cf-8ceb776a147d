processManagement:
    fork: true
    pidFilePath: /data/mongodb/dc3/mongos-cluster/node-01/mongos.pid

systemLog:
    destination: file
    logAppend: true
    path: /data/mongodb/dc3/mongos-cluster/node-01/logs/mongos.log

net:
    # 设置 mongod 监听端口
    port: 27090
    maxIncomingConnections: 10000
    bindIpAll: true

sharding:
    # 这里的的 dc3_replica 必须和 mongs configDB 配置名称一致
    # 这里的三个地址为 mongo-cfg 集群的地址
    configDB: dc3_replica/127.0.0.1:27080,127.0.0.1:27081,127.0.0.1:27082

#不带认证需要屏蔽这两行配置
#security:
#    keyFile: /data/mongodb/dc3/mongos-cluster/node-01/keys/keyfile
#    clusterAuthMode: keyFile
#    authorization: enabled